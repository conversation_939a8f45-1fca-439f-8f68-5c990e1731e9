# 工作回顾模块设计文档

## 一、功能定位

**目标**：为管理者提供基于日报数据的团队工作量统计，以表格形式展示各项指标和同期对比。

**用户群体**：管理员和部门主管

**核心价值**：
- 帮助管理者了解团队工作状态和趋势
- 提供多维度的工作量统计分析
- 支持同期对比，便于发现问题和改进方向

## 二、核心统计维度

### 2.1 时间维度
- **时间范围**：本周、本月、本季度、本年
- **对比分析**：与上期对比（如本月vs上月）

### 2.2 团队维度
- **部门统计**：按部门统计工作量
- **人员统计**：查看指定人员的工作量

## 三、基于日报的核心工作量指标

所有工作量指标都来自日报数据：

### 3.1 基础工作量指标
- **电话量**：`phone_count` - 拨打电话次数
- **拜访量**：`visit_count` - 客户拜访次数
- **邮件量（精发）**：`email_precise_count` - 精准发送邮件数量
- **邮件量（粗发）**：`email_bulk_count` - 批量发送邮件数量
- **社媒量**：`social_media_count` - 社交媒体联系次数
- **回复量**：`reply_count` - 客户回复处理次数
- **演示量**：`demo_count` - 产品演示次数

### 3.2 客户相关指标
- **新增客户数**：`new_customer_count` - 新增客户数量
- **跟进客户数**：`follow_customer_count` - 跟进客户数量
- **签约客户数**：`contract_customer_count` - 签约客户数量

### 3.3 报告相关指标
- **日报总数**：指定时间范围内的日报总数量
- **按时完成率**：按时提交的日报占比
- **延期报告数**：超期提交的日报数量

## 四、数据来源分析

### 4.1 主要数据表
- `crm_report_work_data` - 工作量数据表（核心数据源，只统计日报）
- `crm_report_base` - 报告基础表（日报状态和提交情况）

### 4.2 统计逻辑
- **工作量统计**：从 `crm_report_work_data` 表按 `DataType` 字段统计，只统计 `ReportType = 1`（日报）
- **报告统计**：从 `crm_report_base` 表按报告类型和时间范围统计，主要关注日报
- **完成率统计**：基于日报提交时间和截止时间计算

## 五、页面设计（纯表格展示）

### 5.1 筛选条件区域
```
┌─────────────────────────────────────┐
│ 时间范围: [本月▼]  部门: [全部▼]      │
│ 人员选择: [全部▼]  对比: [是/否]      │
└─────────────────────────────────────┘
```

### 5.2 汇总统计表格
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 汇总统计表                                                                │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────────┬─────┤
│ 指标类型    │ 本月数量    │ 上月数量    │ 环比变化    │ 变化幅度    │ 趋势 │
├─────────────┼─────────────┼─────────────┼─────────────┼─────────────┼─────┤
│ 电话量      │    156      │    142      │    +14      │   +9.9%     │  ↗  │
│ 拜访量      │     45      │     38      │     +7      │  +18.4%     │  ↗  │
│ 邮件量(精发)│     89      │     76      │    +13      │  +17.1%     │  ↗  │
│ 邮件量(粗发)│     67      │     58      │     +9      │  +15.5%     │  ↗  │
│ 社媒量      │    234      │    198      │    +36      │  +18.2%     │  ↗  │
│ 回复量      │    123      │    115      │     +8      │   +7.0%     │  ↗  │
│ 演示量      │     23      │     19      │     +4      │  +21.1%     │  ↗  │
│ 新增客户    │     25      │     18      │     +7      │  +38.9%     │  ↗  │
│ 跟进客户    │     78      │     65      │    +13      │  +20.0%     │  ↗  │
│ 签约客户    │     12      │      8      │     +4      │  +50.0%     │  ↗  │
│ 日报总数    │     89      │     82      │     +7      │   +8.5%     │  ↗  │
│ 按时完成率  │    85.4%    │    82.9%    │   +2.5%     │   +3.0%     │  ↗  │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┴─────┘
```

### 5.3 人员详细统计表格（包含同期对比）
```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│ 人员详细统计表                                                                                                                          │
├─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┤
│ 姓名    │ 电话量  │ 拜访量  │ 邮件量  │ 社媒量  │ 回复量  │ 演示量  │ 新增客户│ 跟进客户│ 签约客户│ 电话量  │ 拜访量  │ 邮件量  │ 新增客户│ 跟进客户│ 签约客户│
│         │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (上月)  │ (上月)  │ (上月)  │ (上月)  │ (上月)  │ (上月)  │
├─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┤
│ 张三    │   25    │    8    │   15    │   45    │   20    │    4    │    2    │    2    │    1    │   22    │    7    │   13    │    1    │    2    │    1    │
│ 李四    │   18    │    6    │   12    │   38    │   16    │    3    │    1    │    2    │    1    │   16    │    5    │   10    │    1    │    1    │    1    │
│ 王五    │   22    │    7    │   14    │   42    │   18    │    3    │    2    │    3    │    1    │   20    │    6    │   12    │    1    │    3    │    1    │
│ 赵六    │   20    │    5    │   11    │   35    │   15    │    2    │    1    │    1    │    1    │   18    │    4    │    9    │    1    │    1    │    0    │
│ 钱七    │   16    │    4    │    9    │   28    │   12    │    2    │    0    │    1    │    1    │   14    │    3    │    7    │    0    │    1    │    0    │
│ 孙八    │   19    │    6    │   13    │   40    │   17    │    3    │    1    │    2    │    1    │   17    │    5    │   11    │    1    │    1    │    1    │
│ 周九    │   21    │    7    │   15    │   44    │   19    │    4    │    2    │    2    │    1    │   19    │    6    │   13    │    1    │    2    │    1    │
│ 吴十    │   17    │    5    │   10    │   32    │   14    │    2    │    1    │    1    │    1    │   15    │    4    │    8    │    1    │    1    │    0    │
│ 郑十一  │   23    │    8    │   16    │   46    │   21    │    4    │    2    │    3    │    1    │   21    │    7    │   14    │    1    │    3    │    1    │
│ 王十二  │   15    │    4    │    8    │   26    │   11    │    1    │    0    │    1    │    1    │   13    │    3    │    6    │    0    │    1    │    0    │
├─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┤
│ 合计    │  197    │   60    │  123    │  382    │  163    │   26    │   12    │   18    │   10    │  175    │   50    │  103    │    8    │   16    │    6    │
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘
```

### 5.4 部门统计表格（包含同期对比）
```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│ 部门统计表                                                                                                                          │
├─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┤
│ 部门    │ 人员数  │ 电话量  │ 拜访量  │ 邮件量  │ 社媒量  │ 回复量  │ 新增客户│ 跟进客户│ 签约客户│ 电话量  │ 拜访量  │ 邮件量  │ 新增客户│ 跟进客户│ 签约客户│
│         │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (本月)  │ (上月)  │ (上月)  │ (上月)  │ (上月)  │ (上月)  │ (上月)  │
├─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┤
│ 销售一部│    5    │   85    │   25    │   45    │  125    │   55    │    5    │    7    │    3    │   75    │   20    │   38    │    3    │    5    │    2    │
│ 销售二部│    4    │   72    │   20    │   38    │  108    │   48    │    4    │    6    │    2    │   65    │   18    │   32    │    3    │    4    │    2    │
│ 销售三部│    3    │   55    │   15    │   28    │   85    │   35    │    2    │    4    │    2    │   48    │   12    │   24    │    1    │    4    │    1    │
│ 海外部  │    2    │   35    │   10    │   18    │   55    │   25    │    1    │    1    │    3    │   30    │    8    │   15    │    1    │    3    │    1    │
├─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┤
│ 合计    │   14    │  247    │   70    │  129    │  373    │  163    │   12    │   18    │   10    │  218    │   58    │  109    │    8    │   16    │    6    │
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘
```

## 六、查询条件设计

### 6.1 基础筛选条件
- **时间范围**：本周/本月/本季度/本年
- **部门选择**：选择部门（可选全部）
- **人员选择**：选择人员（可选全部）

### 6.2 对比选项
- **是否对比**：与上期数据对比

## 七、数据查询逻辑

### 7.1 汇总统计查询（包含对比）
```sql
-- 本月数据
SELECT 
    SUM(CASE WHEN DataType = 'phone_count' THEN DataValue ELSE 0 END) as PhoneCount,
    SUM(CASE WHEN DataType = 'visit_count' THEN DataValue ELSE 0 END) as VisitCount,
    SUM(CASE WHEN DataType = 'email_precise_count' THEN DataValue ELSE 0 END) as EmailPreciseCount,
    SUM(CASE WHEN DataType = 'email_bulk_count' THEN DataValue ELSE 0 END) as EmailBulkCount,
    SUM(CASE WHEN DataType = 'social_media_count' THEN DataValue ELSE 0 END) as SocialMediaCount,
    SUM(CASE WHEN DataType = 'reply_count' THEN DataValue ELSE 0 END) as ReplyCount,
    SUM(CASE WHEN DataType = 'demo_count' THEN DataValue ELSE 0 END) as DemoCount,
    SUM(CASE WHEN DataType = 'new_customer_count' THEN DataValue ELSE 0 END) as NewCustomerCount,
    SUM(CASE WHEN DataType = 'follow_customer_count' THEN DataValue ELSE 0 END) as FollowCustomerCount,
    SUM(CASE WHEN DataType = 'contract_customer_count' THEN DataValue ELSE 0 END) as ContractCustomerCount
FROM crm_report_work_data 
WHERE ReportType = 1  -- 只统计日报
    AND ReportDate BETWEEN ? AND ?  -- 本月时间范围
    AND Deleted = 0

-- 上月数据（类似查询，时间范围改为上月）
```

### 7.2 人员详细统计查询（包含对比）
```sql
-- 本月人员数据
SELECT 
    UserId, UserName, TeamId, TeamName,
    SUM(CASE WHEN DataType = 'phone_count' THEN DataValue ELSE 0 END) as PhoneCount,
    SUM(CASE WHEN DataType = 'visit_count' THEN DataValue ELSE 0 END) as VisitCount,
    SUM(CASE WHEN DataType = 'email_precise_count' THEN DataValue ELSE 0 END) as EmailPreciseCount,
    SUM(CASE WHEN DataType = 'email_bulk_count' THEN DataValue ELSE 0 END) as EmailBulkCount,
    SUM(CASE WHEN DataType = 'social_media_count' THEN DataValue ELSE 0 END) as SocialMediaCount,
    SUM(CASE WHEN DataType = 'reply_count' THEN DataValue ELSE 0 END) as ReplyCount,
    SUM(CASE WHEN DataType = 'demo_count' THEN DataValue ELSE 0 END) as DemoCount,
    SUM(CASE WHEN DataType = 'new_customer_count' THEN DataValue ELSE 0 END) as NewCustomerCount,
    SUM(CASE WHEN DataType = 'follow_customer_count' THEN DataValue ELSE 0 END) as FollowCustomerCount,
    SUM(CASE WHEN DataType = 'contract_customer_count' THEN DataValue ELSE 0 END) as ContractCustomerCount
FROM crm_report_work_data 
WHERE ReportType = 1  -- 只统计日报
    AND ReportDate BETWEEN ? AND ?  -- 本月时间范围
    AND Deleted = 0
GROUP BY UserId, UserName, TeamId, TeamName

-- 上月人员数据（类似查询，时间范围改为上月）
```

### 7.3 部门统计查询（包含对比）
```sql
-- 本月部门数据
SELECT 
    TeamId, TeamName,
    COUNT(DISTINCT UserId) as UserCount,
    SUM(CASE WHEN DataType = 'phone_count' THEN DataValue ELSE 0 END) as PhoneCount,
    SUM(CASE WHEN DataType = 'visit_count' THEN DataValue ELSE 0 END) as VisitCount,
    SUM(CASE WHEN DataType = 'email_precise_count' THEN DataValue ELSE 0 END) as EmailPreciseCount,
    SUM(CASE WHEN DataType = 'email_bulk_count' THEN DataValue ELSE 0 END) as EmailBulkCount,
    SUM(CASE WHEN DataType = 'social_media_count' THEN DataValue ELSE 0 END) as SocialMediaCount,
    SUM(CASE WHEN DataType = 'reply_count' THEN DataValue ELSE 0 END) as ReplyCount,
    SUM(CASE WHEN DataType = 'demo_count' THEN DataValue ELSE 0 END) as DemoCount,
    SUM(CASE WHEN DataType = 'new_customer_count' THEN DataValue ELSE 0 END) as NewCustomerCount,
    SUM(CASE WHEN DataType = 'follow_customer_count' THEN DataValue ELSE 0 END) as FollowCustomerCount,
    SUM(CASE WHEN DataType = 'contract_customer_count' THEN DataValue ELSE 0 END) as ContractCustomerCount
FROM crm_report_work_data 
WHERE ReportType = 1  -- 只统计日报
    AND ReportDate BETWEEN ? AND ?  -- 本月时间范围
    AND Deleted = 0
GROUP BY TeamId, TeamName

-- 上月部门数据（类似查询，时间范围改为上月）
```

## 八、实现优先级

### 8.1 第一阶段（核心统计表格）
1. **汇总统计表格**：显示所有工作量指标的汇总数据和对比
2. **人员详细统计表格**：显示每个人的详细工作量和上月对比
3. **基础筛选**：时间范围、部门筛选

### 8.2 第二阶段（完善功能）
1. **部门统计表格**：按部门统计工作量和上月对比
2. **排序功能**：支持按各列排序
3. **变化趋势标识**：用颜色或图标标识增长/下降趋势

### 8.3 第三阶段（优化）
1. **分页功能**：大数据量分页显示
2. **性能优化**：大数据量处理
3. **缓存优化**：对常用统计结果进行缓存

## 九、技术实现要点

### 9.1 对比数据计算
- 分别查询本月和上月的数据
- 计算环比变化和变化幅度
- 用颜色或图标标识趋势（增长/下降/持平）

### 9.2 权限控制
- 复用现有的管理员权限检查
- 根据用户部门限制数据范围

### 9.3 性能优化
- 使用 `DbContext.Crm2Db.Queryable`
- 添加必要的数据库索引

## 十、数据模型设计

### 10.1 视图模型
需要创建以下视图模型：
- `VM_WorkReviewSummary` - 汇总统计模型
  - 包含所有工作量指标的汇总数据
  - 包含同期对比数据
  - 包含变化趋势标识
- `VM_WorkReviewPersonDetail` - 人员详细统计模型
  - 包含每个人的详细工作量数据
  - 包含新增客户、跟进客户、签约客户的分别统计
  - 包含同期对比数据
- `VM_WorkReviewDepartmentDetail` - 部门统计模型
  - 包含各部门的工作量汇总数据
  - 包含新增客户、跟进客户、签约客户的分别统计
  - 包含同期对比数据
- `VM_WorkReviewFilter` - 筛选条件模型
  - 时间范围筛选
  - 部门筛选
  - 人员筛选
  - 对比选项

### 10.2 业务逻辑类
需要创建以下业务逻辑类：
- `BLL_WorkReview` - 工作回顾业务逻辑主类
- `BLL_WorkReview.Statistics.cs` - 统计功能
- `BLL_WorkReview.Comparison.cs` - 对比功能

### 10.3 控制器接口
需要创建以下控制器接口：
- `GetWorkReviewSummary` - 获取汇总统计
- `GetWorkReviewPersonDetail` - 获取人员详细统计
- `GetWorkReviewDepartmentDetail` - 获取部门统计

## 十一、开发计划

### 11.1 开发时间安排
- **第一阶段**：2周（核心统计表格）
- **第二阶段**：1周（完善功能）
- **第三阶段**：1周（优化）

### 11.2 测试计划
- **单元测试**：业务逻辑层测试
- **集成测试**：接口功能测试
- **性能测试**：大数据量处理测试

### 11.3 部署计划
- **开发环境**：功能验证
- **测试环境**：性能测试
- **生产环境**：正式部署

## 十二、风险评估

### 12.1 技术风险
- **大数据量处理**：可能影响查询性能

### 12.2 业务风险
- **数据准确性**：确保统计数据的准确性
- **权限控制**：确保数据访问的安全性

### 12.3 缓解措施
- **性能优化**：添加数据库索引，优化查询语句
- **权限验证**：严格的数据访问权限控制

---

**文档版本**：v1.0  
**创建日期**：2025年1月  
**最后更新**：2025年1月  
**状态**：设计完成，待开发 