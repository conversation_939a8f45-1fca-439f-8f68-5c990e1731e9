using CRM2_API.BLL.Common;
using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.BusinessModel;
using CRM2_API.Model.ControllersViewModel;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.VariantTypes;
using LgyUtil;
using Lucene.Net.Support;
using NPOI.SS.Formula.Functions;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SqlSugar;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.Linq;
using CRM2_API.Model.BLLModel.Enum;
using static CRM2_API.Model.ControllersViewModel.VM_Contract;
using CRM2_API.Model.System;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_contract_productinfo表操作
    /// </summary>
    public class DbOpe_crm_contract_productinfo : DbOperateCrm2Ex<Db_crm_contract_productinfo, DbOpe_crm_contract_productinfo>
    {
        //public void AddContractProductinfo(ProductInfo productInfo, string contractId, string userId)
        //{
        //    Db_crm_contract_productinfo ContractProductinfo = productInfo.MappingTo<Db_crm_contract_productinfo>();
        //    ContractProductinfo.Id = Guid.NewGuid().ToString();
        //    ContractProductinfo.ContractId = contractId;
        //    ContractProductinfo.Deleted = false;
        //    ContractProductinfo.CreateUser = userId;
        //    ContractProductinfo.CreateDate = DateTime.Now;
        //    Insert(ContractProductinfo);
        //}

        public void AddContractProductinfo(List<Db_crm_contract_productinfo> productInfo)
        {
            //List<Db_crm_contract_productinfo> ContractProductinfo = new List<Db_crm_contract_productinfo>();
            //productInfo.ForEach(p =>
            //{
            //    ProductPrice productPrice = productPrices.Where(r => r.Currency == Currency && r.ProductId == p.ProductId.ToString()).First();
            //    if (productPrice == null)
            //    {
            //        productPrice = productPrices.Where(r => r.Currency == Currency && r.ProductId == p.ProductId.ToString()).OrderBy(r => r.Currency).First();
            //    }

            //    Db_crm_contract_productinfo info = p.MappingTo<Db_crm_contract_productinfo>();
            //    info.Id = Guid.NewGuid().ToString();
            //    info.ContractId = contractId;

            //    info.ProductName = productPrice.ProductName;
            //    info.ProductPrice = productPrice.CurrencyPrice;

            //    info.Deleted = false;
            //    info.CreateUser = userId;
            //    info.CreateDate = DateTime.Now;
            //    ContractProductinfo.Add(info);
            //});
            Insert(productInfo);
        }

        //public void AddContractProductinfo(List<AddProductInfo_In> productInfo, string contractId, string userId)
        //{
        //    List<Db_crm_contract_productinfo> ContractProductinfo = new List<Db_crm_contract_productinfo>();
        //    productInfo.ForEach(p =>
        //    {
        //        Db_crm_contract_productinfo info = p.MappingTo<Db_crm_contract_productinfo>();
        //        info.Id = Guid.NewGuid().ToString();
        //        info.ContractId = contractId;
        //        info.Deleted = false;
        //        info.CreateUser = userId;
        //        info.CreateDate = DateTime.Now;
        //        ContractProductinfo.Add(info);
        //    });
        //    Insert(ContractProductinfo);
        //}

        //public void UpdateContractProductinfo(List<UpdateProductInfo_In> productInfo, string contractId, string userId)
        //{
        //    List<Db_crm_contract_productinfo> ContractProductinfo = new List<Db_crm_contract_productinfo>();
        //    List<UpdateProductInfo_In> addProductInfo = productInfo.Where(r => r.Id == Guid.Empty.ToString()).ToList();
        //    addProductInfo.ForEach(p =>
        //    {
        //        Db_crm_contract_productinfo info = p.MappingTo<Db_crm_contract_productinfo>();
        //        info.Id = Guid.NewGuid().ToString();
        //        info.ContractId = contractId;
        //        info.Deleted = false;
        //        info.CreateUser = userId;
        //        info.CreateDate = DateTime.Now;
        //        ContractProductinfo.Add(info);
        //    });
        //    Insert(ContractProductinfo);
        //    List<UpdateProductInfo_In> updateProductInfo = productInfo.Where(r => r.Id != Guid.Empty.ToString()).ToList();
        //    updateProductInfo.ForEach(p =>
        //    {
        //        Updateable.SetColumns(t => new Db_crm_contract_productinfo()
        //        {
        //            ContractId = contractId,
        //            ProductId = p.ProductId.ToString(),
        //            OpeningMonths = p.OpeningMonths,
        //            Countrys = p.Countrys,
        //            PrimaryAccountsNum = p.PrimaryAccountsNum,
        //            SubAccountsNum = p.SubAccountsNum,
        //            CodesNum = p.CodesNum,
        //            SetNum = p.SetNum,
        //            PeriodsNum = p.PeriodsNum,
        //            UpdateDate = DateTime.Now,
        //            UpdateUser = userId
        //        }).Where(e => e.Id == p.Id).ExecuteCommand();
        //    });
        //    List<Db_crm_contract_productinfo> DeleteproductInfo = Queryable.Where(r => r.ContractId == contractId && r.Deleted == false).ToList();
        //    DeleteproductInfo.ForEach(p =>
        //    {
        //        if (!updateProductInfo.Where(r => r.Id == p.Id).Any())
        //        {
        //            Updateable
        //            .SetColumns(e => e.Deleted == true)
        //            .Where(e => e.Id == p.Id)
        //            .ExecuteCommand();
        //        }
        //    });
        //}

        public void UpdateContractProductinfoDataList(List<Db_crm_contract_productinfo> productInfo, string contractId)
        {
            List<string> ids = Queryable.Where(r => r.ContractId == contractId && r.Deleted == false).ToList().Select(r => r.Id).ToList();
            UpdateDataList<Db_crm_contract_productinfo>(productInfo, ids);
        }

        //public void UpdateContractProductinfoDataList(List<UpdateProductInfo_In> productInfo, string contractId)
        //{
        //    List<string> ids = Queryable.Where(r => r.ContractId == contractId && r.Deleted == false).ToList().Select(r => r.Id).ToList();
        //    UpdateDataList<UpdateProductInfo_In>(productInfo, ids);
        //}

        //public void UpdateContractProductinfos(List<UpdateProductInfo_In> productInfo, string contractId, string userId, List<Db_crm_contract_productinfo> DBProductInfo)
        //{
        //    List<Db_crm_contract_productinfo> ContractProductinfo = new List<Db_crm_contract_productinfo>();
        //    List<UpdateProductInfo_In> addProductInfo = productInfo.Where(r => r.Id == Guid.Empty.ToString()).ToList();
        //    addProductInfo.ForEach(p =>
        //    {
        //        Db_crm_contract_productinfo info = p.MappingTo<Db_crm_contract_productinfo>();
        //        info.Id = Guid.NewGuid().ToString();
        //        info.ContractId = contractId;
        //        info.Deleted = false;
        //        info.CreateUser = userId;
        //        info.CreateDate = DateTime.Now;
        //        ContractProductinfo.Add(info);
        //    });
        //    Insert(ContractProductinfo);
        //    List<UpdateProductInfo_In> updateProductInfo = productInfo.Where(r => r.Id != Guid.Empty.ToString()).ToList();
        //    updateProductInfo.ForEach(p =>
        //    {
        //        Db_crm_contract_productinfo pi = p.MappingTo<Db_crm_contract_productinfo>();
        //        pi.UpdateUser = userId;
        //        pi.UpdateDate = DateTime.Now;
        //        Db.Updateable<Db_crm_contract_productinfo>(pi).UpdateColumns(t => new UpdateProductInfo_In()
        //        { }).Where(e => e.Id == pi.Id).ExecuteCommand();



        //        Updateable.SetColumns(t => new Db_crm_contract_productinfo()
        //        {
        //            ContractId = contractId,
        //            ProductId = p.ProductId.ToString(),
        //            OpeningMonths = p.OpeningMonths,
        //            Countrys = p.Countrys,
        //            PrimaryAccountsNum = p.PrimaryAccountsNum,
        //            SubAccountsNum = p.SubAccountsNum,
        //            CodesNum = p.CodesNum,
        //            SetNum = p.SetNum,
        //            PeriodsNum = p.PeriodsNum,
        //            UpdateDate = DateTime.Now,
        //            UpdateUser = userId
        //        }).Where(e => e.Id == p.Id).ExecuteCommand();
        //    });
        //    List<Db_crm_contract_productinfo> DeleteproductInfo = Queryable.Where(r => r.ContractId == contractId && r.Deleted == false).ToList();
        //    DeleteproductInfo.ForEach(p =>
        //    {
        //        if (!updateProductInfo.Where(r => r.Id == p.Id).Any())
        //        {
        //            Updateable
        //            .SetColumns(e => e.Deleted == true)
        //            .Where(e => e.Id == p.Id)
        //            .ExecuteCommand();
        //        }
        //    });
        //}

        public void DeleteContractProductinfoByContractId(string contractId)
        {
            DeleteData(r => r.ContractId == contractId);
        }

        public List<ProductServiceInfo_Out> GetContractServiceInfo(string contractId, bool dataOwner = false)
        {
            int Pass = EnumProcessStatus.Pass.ToInt();
            var list = Queryable
                .LeftJoin<Db_crm_product>((r, p) => r.ProductId == p.Id)// && p.Deleted == false)
                .LeftJoin<Db_crm_contract>((r, p, c) => r.ContractId == c.Id && c.Deleted == false)
                //.LeftJoin<Db_sys_product_month_year>((r, p, c, pmy) => r.OpeningMonths == pmy.Month && pmy.Deleted == false)
                .LeftJoin<Db_crm_product_price>((r, p, c, pp) => r.ProductPriceId == pp.Id && pp.Deleted == false)
                //.LeftJoin<Db_crm_product_price>((r, p, c, pmy, pp) => r.ProductId == pp.ProductId && c.Currency == pp.Currency && pp.Deleted == false && pp.ServiceCycle == pmy.Year)
                .LeftJoin<Db_v_productserviceinfo>((r, p, c, pp, vp) => r.Id == vp.Id)
                .LeftJoin<Db_v_contract_productserviceinfo_statewith_validout>((r, p, c, pp, vp, sp) => r.Id == sp.ContractProductInfoId)
                .LeftJoin<Db_v_customer_subcompany_private_user>((r, p, c, pp, vp, sp, cspu) => cspu.Id == c.FirstParty)
                .Where((r, p, c, pp, vp, sp, cspu) => r.ContractId == contractId && r.Deleted == false && vp.ApplState == Pass) //2025年3月7日 修改
                                                                                                                                //.Where((r, p, c) => c.ProtectionDeadline > DateTime.Now) // 服务变更申请不再限制是否超过保护截止日
                .WhereIF(dataOwner, DataOwner.BusinessData("r.CreateUser", "r.ContractId", "cspu.CompanyCurrentUser"))
                .Select((r, p, c, pp, vp, sp, cspu) => new ProductServiceInfo_Out
                {
                    Id = r.Id.SelectAll(),
                    //ProductName = p.ProductName,
                    ProductNameEN = p.ProductNameEN,
                    ProductDescription = p.ProductDescription,
                    ProductDescriptionEn = p.ProductDescriptionEn,
                    ProductType = (int)p.ProductType,
                    Currency = c.Currency.Value,
                    Price = pp.Price,
                    ChargingParameters = pp.ChargingParameters,
                    ProductServiceInfoApplId = vp.applId,
                    ProductServiceInfoApplState = vp.ApplState,
                    ProductServiceInfoApplProcessingType = vp.ProcessingType,
                    ServiceCycle = r.OpeningYears,//pmy.Year,//p.ServiceCycle,
                    ServiceCycleStart = p.ServiceCycleStart,
                    ServiceCycleEnd = p.ServiceCycleEnd,
                    ProductServiceInfo_CustomerCompanyName = sp.CustomerCompanyName,
                    ProductServiceInfo_UsersNo = string.IsNullOrEmpty(sp.UsersNo) ? null : Convert.ToInt32(sp.UsersNo),
                    ProductServiceInfo_SubscriptionPeriod = sp.SubscriptionPeriod,
                    ProductServiceInfo_Countrys = sp.Countrys,
                    ProductServiceInfo_PrimaryAccountsNum = sp.PrimaryAccountsNum,
                    ProductServiceInfo_SubAccountsNum = sp.SubAccountsNum,
                    ProductServiceInfo_SharePeopleNum = sp.SharePeopleNum,
                    ProductServiceInfo_CodesNum = sp.CodesNum,
                    ProductServiceInfo_CaseNum = sp.CaseNum,
                    ProductServiceInfo_PeriodsNum = sp.PeriodsNum,
                    ProductServiceInfo_ExpenseType = sp.ExpenseType,
                    ProductServiceInfo_SelfExpenseAmount = sp.SelfExpenseAmount,
                    ProductServiceInfo_ServiceCycleStart = sp.ServiceCycleStart == null ? vp.ServiceCycleStart : sp.ServiceCycleStart,
                    ProductServiceInfo_ServiceCycleEnd = sp.ServiceCycleEnd == null ? vp.ServiceCycleEnd : sp.ServiceCycleEnd,
                    ProductServiceInfo_ServiceMonth = sp.ServiceMonth
                }).ToList();

            foreach (ProductServiceInfo_Out p in list)
            {
                if (p.ProductType == EnumProductType.Vip.ToInt())
                {
                    List<int> countrys = p.Countrys.Split(",").Select(s => Convert.ToInt32(s)).ToList();
                    int countrysCount = DbOpe_sys_g4_dbnames.Instance.GetDataList(r => countrys.Contains(r.SID.Value)).GroupBy(r => r.BelongToSid).Count();
                    p.ProductServiceInfo_Countrys = countrysCount.ToString();
                }

                if (p.ProductType == EnumProductType.Gtis.ToInt() || p.ProductType == EnumProductType.Vip.ToInt())
                {
                    var gtis = DbOpe_crm_contract_serviceinfo_gtis_user.Instance.GetUserListByContractId(p.ContractId);
                    p.ProductServiceInfo_SubAccountsNum = gtis.Count(e => e.AccountType == (int)EnumGtisAccountType.Sub).ToString();
                }

                /* if (p.ProductType == EnumProductType.Gtis.ToInt())
                 {
                     var discountType = Db.Queryable<Db_crm_contract_serviceinfo_gtis>().Where(e => e.ContractId == p.ContractId && e.ProcessingType == (int)EnumProcessingType.Add && e.IsApplHistory == false).First()?.DiscountType;
                     if (discountType != null)
                         p.DiscountType = (EnumGtisDiscountType)discountType;
                 }*/
            }
            //如果产品列表中包含环球搜，要验证当前合同是否包含gtis或vip零售
            //修改 2025年3月7日 判断当服务中gtis类服务未通过情况下去掉环球搜服务
            if (list.Any(p => p.ProductType == (int)EnumProductType.Global) && Db.Queryable<Db_v_productserviceinfo>().Any(e => e.ContractId == contractId && (e.ProductType == (int)EnumProductType.Gtis || e.ProductType == (int)EnumProductType.Vip) && e.ApplState == 1))
            {
                var toRemoveObj = list.Where(e => e.ProductType == (int)EnumProductType.Global).ToList().First();
                list.Remove(toRemoveObj);
            }
            return list;
        }

        public List<ProductInfo_Out> GetContractProductInfoByContractId(string contractId, bool dataOwner = false)
        {
            int Void = EnumProcessStatus.Void.ToInt();
            int Submit = EnumProcessStatus.Submit.ToInt();
            int Pass = EnumProcessStatus.Pass.ToInt();
            List<ProductInfo_Out> list = Queryable
                .LeftJoin<Db_crm_product>((r, p) => r.ProductId == p.Id)//&& p.Deleted == false)
                .LeftJoin<Db_crm_contract>((r, p, c) => r.ContractId == c.Id && c.Deleted == false)
                //.LeftJoin<Db_sys_product_month_year>((r, p, c, pmy) => r.OpeningMonths == pmy.Month && pmy.Deleted == false)
                .LeftJoin<Db_crm_product_price>((r, p, c, pp) => r.ProductPriceId == pp.Id && pp.Deleted == false)
                //.LeftJoin<Db_crm_product_price>((r, p, c, pmy, pp) => r.ProductId == pp.ProductId && c.Currency == pp.Currency && pp.Deleted == false && pp.ServiceCycle == pmy.Year)
                .LeftJoin<Db_v_productserviceinfo>((r, p, c, pp, vp) => r.Id == vp.Id && (vp.ApplState == Submit || vp.ApplState == Pass))//vp.ApplState != Void &&
                .LeftJoin<Db_v_customer_subcompany_private_user>((r, p, c, pp, vp, cspu) => cspu.Id == c.FirstParty)
                .Where((r, p, c, pp, vp, cspu) => r.ContractId == contractId && r.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("r.CreateUser", "r.ContractId", "cspu.CompanyCurrentUser"))
                .Select((r, p, c, pp, vp, cspu) => new ProductInfo_Out
                {
                    Id = r.Id.SelectAll(),
                    //ProductName = p.ProductName,
                    ProductNameEN = p.ProductNameEN,
                    ProductNum = p.ProductNum,
                    ProductDescription = p.ProductDescription,
                    ProductDescriptionEn = p.ProductDescriptionEn,
                    ProductType = (int)p.ProductType,
                    Currency = c.Currency.Value,
                    Price = pp.Price,
                    ChargingParameters = pp.ChargingParameters,
                    ProductServiceInfoApplId = vp.applId,
                    ProductServiceInfoApplState = vp.ApplState,
                    ProductServiceInfoApplProcessingType = vp.ProcessingType,
                    ServiceCycle = r.OpeningYears,//pmy.Year,//p.ServiceCycle,
                    ServiceCycleStart = p.ServiceCycleStart,
                    ServiceCycleEnd = p.ServiceCycleEnd,
                    Remark = p.Remark,
                    NotForSale = p.NotForSale,
                    ServiceEndDate = vp.ServiceCycleEnd,
                    ServiceStartDate = vp.ServiceCycleStart,
                    OpenServiceCycle = (vp.ServiceCycleStart == null || vp.ServiceCycleEnd == null) ? "--" : vp.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + vp.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"),
                    ServiceState = vp.ServiceState,
                    ServiceRemark = vp.Remark
                }, true).ToList();
            string EmptyParentProductId = Guid.Empty.ToString();
            List<ProductInfo_Out> ParentProductInfoList = list
                .Where(r => r.ParentProductId != null && r.ParentProductId != EmptyParentProductId)
                .GroupBy(r => new { r.ParentProductId, r.ParentProductName, r.ParentBriefIntroduction, r.ParentProductPrice, r.ParentProductPriceId, r.ParentContractProductinfoPrice, r.ContractId, r.Currency, r.ServiceCycleStart, r.ServiceCycleEnd, r.ChargingParameters, r.CreateUser })
                .Select(r => new ProductInfo_Out
                {
                    Id = EmptyParentProductId,
                    ContractId = r.Key.ContractId,
                    ProductId = r.Key.ParentProductId,
                    ProductName = r.Key.ParentProductName,
                    BriefIntroduction = r.Key.ParentBriefIntroduction,
                    ProductPrice = r.Key.ParentProductPrice,
                    ProductPriceId = r.Key.ParentProductPriceId,
                    ContractProductinfoPrice = r.Key.ParentContractProductinfoPrice,
                    OpeningMonths = r.Max(r => r.OpeningMonths),//r=>r.Key.OpeningMonths
                    FirstOpeningMonths = r.Max(r => r.FirstOpeningMonths),//r=>r.Key.FirstOpeningMonths
                    OpeningYears = r.Max(r => r.OpeningYears),
                    ServiceCycle = r.Max(r => r.ServiceCycle),//r.Key.ServiceCycle
                    Currency = r.Key.Currency,
                    ServiceCycleStart = r.Key.ServiceCycleStart,
                    ServiceCycleEnd = r.Key.ServiceCycleEnd,
                    ProductType = 2,
                    Price = r.Key.ParentContractProductinfoPrice,
                    ChargingParameters = r.Key.ChargingParameters,
                    CreateUser = r.Key.CreateUser,
                    ParentProductId = Guid.Empty.ToString(),
                    ParentProductPriceId = Guid.Empty.ToString(),
                    NotForSale = r.Max(r => r.NotForSale),
                    SubAccountsNum = r.Max(r => r.SubAccountsNum),
                    SubAccountsProductPriceId = Guid.Empty.ToString()
                })
                .ToList();
            foreach (ProductInfo_Out ParentProductInfo in ParentProductInfoList)
            {
                var parentProudct = Db.Queryable<Db_crm_product>().Where(p => p.Id == ParentProductInfo.ProductId).Select<Db_crm_product>().First();
                string parentProudctNum = parentProudct.ProductNum;// Db.Queryable<Db_crm_product>().Where(p => p.Id == ParentProductInfo.ProductId).Select(p => p.ProductNum).First();
                List<ProductInfo_Out> ChildNode = list.Where(r => r.ParentProductId == ParentProductInfo.ProductId).ToList();
                ChildNode.ForEach(child =>
                {
                    if (child.ProductType == (int)EnumProductType.Gtis)
                        child.CouponList = DbOpe_crm_customer_coupon.Instance.GetCouponDetailByContractId(child.ContractId);
                });
                ParentProductInfo.ProductDescription = parentProudct.ProductDescription;
                ParentProductInfo.ProductDescriptionEn = parentProudct.ProductDescriptionEn;
                ParentProductInfo.ChildNode = ChildNode;
                ParentProductInfo.ProductNum = parentProudctNum;
            }
            List<ProductInfo_Out> ProductInfo = list.Where(r => r.ParentProductId == null || r.ParentProductId == EmptyParentProductId).ToList();
            List<ProductInfo_Out> result = new List<ProductInfo_Out>();
            result.AddRange(ProductInfo);
            result.AddRange(ParentProductInfoList);

            result.ForEach(it =>
            {
                if (it.ProductType == (int)EnumProductType.Gtis)
                    it.CouponList = DbOpe_crm_customer_coupon.Instance.GetCouponDetailByContractId(it.ContractId);
            });

            foreach (ProductInfo_Out p in result)
            {
                if (p.ProductType == EnumProductType.Vip.ToInt())
                {
                    List<ProductInfo_Out> gtis = result.Where(r => r.ProductType == EnumProductType.Gtis.ToInt()).ToList();
                    if (gtis.Count() > 0)
                    {
                        p.ServiceEndDate = gtis.First().ServiceEndDate;
                        p.ServiceStartDate = gtis.First().ServiceStartDate;
                        p.OpenServiceCycle = gtis.First().OpenServiceCycle;
                        p.ProductServiceInfoApplState = gtis.First().ProductServiceInfoApplState;
                    }
                }
            }
            return result;
        }

        public List<ProductInfoAndPrice_Out> GetContractProductInfoAndPriceByContractId(string contractId, bool dataOwner = false)
        {
            int Void = EnumProcessStatus.Void.ToInt();
            int Submit = EnumProcessStatus.Submit.ToInt();
            int Pass = EnumProcessStatus.Pass.ToInt();
            List<ProductInfoAndPrice_Out> list = Queryable
                .LeftJoin<Db_crm_product>((r, p) => r.ProductId == p.Id)// && p.Deleted == false)
                .LeftJoin<Db_crm_contract>((r, p, c) => r.ContractId == c.Id && c.Deleted == false)
                //.LeftJoin<Db_sys_product_month_year>((r, p, c, pmy) => r.OpeningMonths == pmy.Month && pmy.Deleted == false)
                .LeftJoin<Db_crm_product_price>((r, p, c, pp) => r.ProductPriceId == pp.Id)// && pp.Deleted == false)
                                                                                           //.LeftJoin<Db_crm_product_price>((r, p, c, pmy, pp) => r.ProductId == pp.ProductId && c.Currency == pp.Currency && pp.Deleted == false && pp.ServiceCycle == pmy.Year)
                .LeftJoin<Db_v_productserviceinfo>((r, p, c, pp, vp) => r.Id == vp.Id && (vp.ApplState == Submit || vp.ApplState == Pass))//vp.ApplState != Void &&
                            .LeftJoin<Db_v_customer_subcompany_private_user>((r, p, c, pp, vp, cspu) => cspu.Id == c.FirstParty)
                .Where((r, p, c, pp, vp, cspu) => r.ContractId == contractId && r.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("r.CreateUser", "r.ContractId", "cspu.CompanyCurrentUser"))
                .Select((r, p, c, pp, vp, cspu) => new ProductInfoAndPrice_Out
                {
                    Id = r.Id.SelectAll(),
                    //ProductName = p.ProductName,
                    ProductNameEN = p.ProductNameEN,
                    ProductDescription = p.ProductDescription,
                    ProductDescriptionEn = p.ProductDescriptionEn,
                    ProductType = (int)p.ProductType,
                    Currency = c.Currency.Value,
                    Price = pp.Price,
                    ChargingParameters = pp.ChargingParameters,
                    ProductServiceInfoApplId = vp.applId,
                    ProductServiceInfoApplState = vp.ApplState,
                    ProductServiceInfoApplProcessingType = vp.ProcessingType,
                    ServiceCycle = r.OpeningYears,//pmy.Year,//p.ServiceCycle,
                    ServiceCycleStart = p.ServiceCycleStart,
                    ServiceCycleEnd = p.ServiceCycleEnd,
                    CountrysName = SqlFunc.Subqueryable<Db_sys_g4_dbnames>().
                               Where(d => r.Countrys.Contains(d.SID.ToString()) && d.Deleted == false)
                               .SelectStringJoin(d => d.CountryName, ","),
                    Remark = p.Remark
                }).ToList();

            Db.ThenMapper(list, item =>
            {

                item.ProductPriceList = Db.Queryable<Db_crm_product_price>()
                    .LeftJoin<Db_crm_product>((e, p) => e.ProductId == p.Id)// && p.Deleted == false)
                    .Where((e, p) => e.Deleted == false)
                    .Select((e, p) => new ProductPrice
                    {
                        Id = e.Id,
                        ProductId = e.ProductId,
                        ProductName = p.ProductName,
                        Currency = e.Currency,
                        Price = e.Price,
                        ServiceCycle = e.ServiceCycle,
                        ChargingParameters = e.ChargingParameters
                    })
                    .OrderBy(e => new { e.ServiceCycle, e.Currency })
                    .SetContext(e => e.ProductId, () => item.ProductId, item)
                    .ToList();
            });


            string EmptyParentProductId = Guid.Empty.ToString();
            List<ProductInfoAndPrice_Out> ParentProductInfoList = list
                .Where(r => r.ParentProductId != null && r.ParentProductId != EmptyParentProductId)
                .GroupBy(r => new { r.ParentProductId, r.ParentProductName, r.ParentProductPrice, r.ParentProductPriceId, r.ParentContractProductinfoPrice, r.ParentContractProductinfoPriceTotal, r.ContractId, r.Currency, r.ServiceCycleStart, r.ServiceCycleEnd, r.ChargingParameters, r.CreateUser })
                .Select(r => new ProductInfoAndPrice_Out
                {
                    Id = EmptyParentProductId,
                    ContractId = r.Key.ContractId,
                    ProductId = r.Key.ParentProductId,
                    ProductName = r.Key.ParentProductName,
                    ProductPrice = r.Key.ParentProductPrice,
                    ProductPriceId = r.Key.ParentProductPriceId,
                    ContractProductinfoPrice = r.Key.ParentContractProductinfoPrice,
                    ContractProductinfoPriceTotal = r.Key.ParentContractProductinfoPriceTotal,
                    OpeningMonths = r.Max(r => r.OpeningMonths),//r.Key.OpeningMonths,
                    FirstOpeningMonths = r.Max(r => r.FirstOpeningMonths),//r.Key.FirstOpeningMonths,
                    OpeningYears = r.Max(r => r.OpeningYears),
                    ServiceCycle = r.Max(r => r.ServiceCycle),//r.Key.ServiceCycle
                    Currency = r.Key.Currency,
                    ServiceCycleStart = r.Key.ServiceCycleStart,
                    ServiceCycleEnd = r.Key.ServiceCycleEnd,
                    ProductType = 2,
                    Price = r.Key.ParentContractProductinfoPrice,
                    ChargingParameters = r.Key.ChargingParameters,
                    CreateUser = r.Key.CreateUser,
                    ParentProductId = Guid.Empty.ToString(),
                    ParentProductPriceId = Guid.Empty.ToString(),
                    SubAccountsNum = r.Max(r => r.SubAccountsNum),
                    SubAccountsProductPriceId = Guid.Empty.ToString()
                })
                .ToList();
            foreach (ProductInfoAndPrice_Out ParentProductInfo in ParentProductInfoList)
            {
                List<ProductInfo_Out> ChildNode = list
                    .Where(r => r.ParentProductId == ParentProductInfo.ProductId)
                    .Select(r => new ProductInfo_Out
                    {
                        Id = r.Id,
                        ContractId = r.ContractId,
                        ProductId = r.ProductId,
                        ProductName = r.ProductName,
                        ProductNameEN = r.ProductNameEN,
                        ProductDescription = r.ProductDescription,
                        ProductDescriptionEn = r.ProductDescriptionEn,
                        Currency = r.Currency,
                        Price = r.Price,
                        ChargingParameters = r.ChargingParameters,
                        ProductType = r.ProductType,
                        OpeningMonths = r.OpeningMonths,
                        FirstOpeningMonths = r.FirstOpeningMonths,
                        OpeningYears = r.OpeningYears,
                        Countrys = r.Countrys,
                        PrimaryAccountsNum = r.PrimaryAccountsNum,
                        SubAccountsNum = r.SubAccountsNum,
                        CodesNum = r.CodesNum,
                        SetNum = r.SetNum,
                        PeriodsNum = r.PeriodsNum,
                        CreateUser = r.CreateUser,
                        CreateDate = r.CreateDate,
                        ProductServiceInfoApplId = r.ProductServiceInfoApplId,
                        ProductServiceInfoApplState = r.ProductServiceInfoApplState,
                        ProductServiceInfoApplProcessingType = r.ProductServiceInfoApplProcessingType,
                        ServiceCycle = r.ServiceCycle,
                        ServiceCycleStart = r.ServiceCycleStart,
                        ServiceCycleEnd = r.ServiceCycleEnd,
                        ProductPrice = r.ProductPrice,
                        ProductPriceId = r.ProductPriceId,
                        ContractProductinfoPrice = r.ContractProductinfoPrice,
                        ContractProductinfoPriceTotal = r.ContractProductinfoPriceTotal,
                        ParentProductId = r.ParentProductId,
                        ParentProductName = r.ParentProductName,
                        ParentProductPrice = r.ParentProductPrice,
                        ParentProductPriceId = r.ParentProductPriceId,
                        ParentContractProductinfoPrice = r.ParentContractProductinfoPrice,
                        ParentContractProductinfoPriceTotal = r.ParentContractProductinfoPriceTotal
                    }).ToList();
                ParentProductInfo.ChildNode = ChildNode;
            }
            Db.ThenMapper(ParentProductInfoList, item =>
            {

                item.ProductPriceList = Db.Queryable<Db_crm_product_price>()
                        .LeftJoin<Db_crm_product>((e, p) => e.ProductId == p.Id)// && p.Deleted == false)
                        .Where((e, p) => e.Deleted == false)
                        .Select((e, p) => new ProductPrice
                        {
                            Id = e.Id,
                            ProductId = e.ProductId,
                            ProductName = p.ProductName,
                            Currency = e.Currency,
                            Price = e.Price,
                            ServiceCycle = e.ServiceCycle,
                            ChargingParameters = e.ChargingParameters
                        })
                        .OrderBy(e => new { e.ServiceCycle, e.Currency })
                        .SetContext(e => e.ProductId, () => item.ProductId, item)
                        .ToList();

            });

            List<ProductInfoAndPrice_Out> ProductInfo = list.Where(r => r.ParentProductId == null || r.ParentProductId == EmptyParentProductId).ToList();
            List<ProductInfoAndPrice_Out> result = new List<ProductInfoAndPrice_Out>();
            result.AddRange(ProductInfo);
            result.AddRange(ParentProductInfoList);
            return result;
        }

        public ProductInfo_Out GetContractProductInfoById(string contractProductId)
        {
            return Queryable
                .LeftJoin<Db_crm_product>((r, p) => r.ProductId == p.Id)// && p.Deleted == false)
                .LeftJoin<Db_crm_contract>((r, p, c) => r.ContractId == c.Id && c.Deleted == false)
                //.LeftJoin<Db_sys_product_month_year>((r, p, c, pmy) => r.OpeningMonths == pmy.Month && pmy.Deleted == false)
                .LeftJoin<Db_crm_product_price>((r, p, c, pp) => r.ProductPriceId == pp.Id && pp.Deleted == false)
                //.LeftJoin<Db_crm_product_price>((r, p, c, pmy, pp) => r.ProductId == pp.ProductId && c.Currency == pp.Currency && pp.Deleted == false && pp.ServiceCycle == pmy.Year)
                .LeftJoin<Db_v_productserviceinfo>((r, p, c, pp, vp) => r.Id == vp.Id)
                .Where((r, p, c, pp, vp) => r.Id == contractProductId && r.Deleted == false)
                .Select((r, p, c, pp, vp) => new ProductInfo_Out
                {
                    Id = r.Id.SelectAll(),
                    //ProductName = p.ProductName,
                    ProductNameEN = p.ProductNameEN,
                    ProductDescription = p.ProductDescription,
                    ProductDescriptionEn = p.ProductDescriptionEn,
                    ProductType = (int)p.ProductType,
                    Currency = c.Currency.Value,
                    Price = pp.Price,
                    ChargingParameters = pp.ChargingParameters,
                    ProductServiceInfoApplId = vp.applId,
                    ProductServiceInfoApplState = vp.ApplState,
                    ProductServiceInfoApplProcessingType = vp.ProcessingType,
                    ServiceCycle = r.OpeningYears,//pmy.Year,//p.ServiceCycle,
                    ServiceCycleStart = p.ServiceCycleStart,
                    ServiceCycleEnd = p.ServiceCycleEnd,
                    CustomizedReportDownloadsNum = r.CustomizedReportDownloadsNum
                }).First();
        }

        public List<ProductInfoAudit_Out> GetContractProductInfoAuditByContractId(string contractId, bool dataOwner = false)
        {
            int Submit = EnumProcessStatus.Submit.ToInt();
            int Pass = EnumProcessStatus.Pass.ToInt();
            var list = Queryable
                .LeftJoin<Db_crm_product>((r, p) => r.ProductId == p.Id)// && p.Deleted == false)
                .LeftJoin<Db_crm_contract>((r, p, c) => r.ContractId == c.Id && c.Deleted == false)
                //.LeftJoin<Db_sys_product_month_year>((r, p, c, pmy) => r.OpeningMonths == pmy.Month && pmy.Deleted == false)
                .LeftJoin<Db_crm_product_price>((r, p, c, pp) => r.ProductPriceId == pp.Id && pp.Deleted == false)
                //.LeftJoin<Db_crm_product_price>((r, p, c, pmy, pp) => r.ProductId == pp.ProductId && c.Currency == pp.Currency && pp.Deleted == false && pp.ServiceCycle == pmy.Year)
                .LeftJoin<Db_v_productserviceinfo>((r, p, c, pp, vp) => r.Id == vp.Id && (vp.ApplState == Submit || vp.ApplState == Pass))
                .LeftJoin<Db_crm_contract_productinfo_audit>((r, p, c, pp, vp, cpa) => r.Id == cpa.ContractProductInfoId && cpa.Deleted == false)
                            .LeftJoin<Db_v_customer_subcompany_private_user>((r, p, c, pp, vp, cpa, cspu) => cspu.Id == c.FirstParty)
                .Where((r, p, c, pp, vp, cpa, cspu) => r.ContractId == contractId && r.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("r.CreateUser", "r.ContractId", "cspu.CompanyCurrentUser"))
                .Select((r, p, c, pp, vp, cpa, cspu) => new ProductInfoAudit_Out
                {
                    Id = r.Id,
                    ContractId = r.ContractId,
                    ProductId = r.ProductId,
                    CreateUser = r.CreateUser,
                    CreateDate = r.CreateDate,
                    ProductName = r.ProductName,
                    //ProductPrice = r.ProductPrice,
                    ProductPriceId = r.ProductPriceId,
                    SubAccountsProductPrice = r.SubAccountsProductPrice,
                    SubAccountsProductPriceId = r.SubAccountsProductPriceId,
                    ParentProductId = r.ParentProductId,
                    ParentProductName = r.ParentProductName,
                    ParentProductPrice = r.ParentProductPrice,
                    ParentProductPriceId = r.ParentProductPriceId,
                    ParentContractProductinfoPrice = r.ParentContractProductinfoPrice,
                    ParentContractProductinfoPriceTotal = r.ParentContractProductinfoPriceTotal,
                    //ContractProductinfoPrice = r.ContractProductinfoPrice,
                    ContractProductinfoPriceTotal = r.ContractProductinfoPrice,
                    ProductType = (int)p.ProductType,
                    Currency = c.Currency.Value,
                    Price = pp.Price,
                    ChargingParameters = pp.ChargingParameters,
                    ServiceCycle = r.OpeningYears,//pmy.Year,//p.ServiceCycle,
                    ServiceCycleStart = p.ServiceCycleStart,
                    ServiceCycleEnd = p.ServiceCycleEnd,
                    ProductServiceInfoApplId = vp.applId,
                    ProductServiceInfoApplState = vp.ApplState,
                    ProductServiceInfoApplProcessingType = vp.ProcessingType,
                    PreOpeningMonths = cpa.OpeningMonths == null ? null : (r.OpeningMonths == null ? null : (r.OpeningMonths.Value == cpa.OpeningMonths.Value ? null : r.OpeningMonths)),
                    PreFirstOpeningMonths = cpa.FirstOpeningMonths == null ? null : (r.FirstOpeningMonths == null ? null : (r.FirstOpeningMonths.Value == cpa.FirstOpeningMonths.Value ? null : r.FirstOpeningMonths)),
                    PreOpeningYears = cpa.OpeningYears == null ? null : (r.OpeningYears == null ? null : (r.OpeningYears.Value == cpa.OpeningYears.Value ? null : r.OpeningYears)),
                    PreCountrys = cpa.Countrys == null ? null : (r.Countrys == null ? null : (r.Countrys == cpa.Countrys ? null : r.Countrys)),
                    PrePrimaryAccountsNum = cpa.PrimaryAccountsNum == null ? null : (r.PrimaryAccountsNum == null ? null : (r.PrimaryAccountsNum.Value == cpa.PrimaryAccountsNum ? null : r.PrimaryAccountsNum)),
                    PreSubAccountsNum = cpa.SubAccountsNum == null ? null : (r.SubAccountsNum == null ? null : (r.SubAccountsNum.Value == cpa.SubAccountsNum.Value ? null : r.SubAccountsNum)),
                    PreCodesNum = cpa.CodesNum == null ? null : (r.CodesNum == null ? null : (r.CodesNum.Value == cpa.CodesNum.Value ? null : r.CodesNum)),
                    PreSetNum = cpa.SetNum == null ? null : (r.SetNum == null ? null : (r.SetNum.Value == cpa.SetNum.Value ? null : r.SetNum)),
                    PrePeriodsNum = cpa.PeriodsNum == null ? null : (r.PeriodsNum == null ? null : (r.PeriodsNum == cpa.PeriodsNum ? null : r.PeriodsNum)),
                    PreProductPrice = cpa.ProductPrice == null ? null : (r.ProductPrice == null ? "" : (r.ProductPrice == cpa.ProductPrice ? null : r.ProductPrice)),

                    OpeningMonths = cpa.OpeningMonths == null ? (r.OpeningMonths == null ? null : r.OpeningMonths.Value) : cpa.OpeningMonths.Value,
                    FirstOpeningMonths = cpa.FirstOpeningMonths == null ? (r.FirstOpeningMonths == null ? null : r.FirstOpeningMonths.Value) : cpa.FirstOpeningMonths.Value,
                    OpeningYears = cpa.OpeningYears == null ? (r.OpeningYears == null ? null : r.OpeningYears.Value) : cpa.OpeningYears.Value,
                    Countrys = cpa.Countrys == null ? (r.Countrys == null ? null : r.Countrys) : cpa.Countrys,
                    PrimaryAccountsNum = cpa.PrimaryAccountsNum == null ? (r.PrimaryAccountsNum == null ? null : r.PrimaryAccountsNum.Value) : cpa.PrimaryAccountsNum.Value,
                    SubAccountsNum = cpa.SubAccountsNum == null ? (r.SubAccountsNum == null ? null : r.SubAccountsNum.Value) : cpa.SubAccountsNum.Value,
                    CodesNum = cpa.CodesNum == null ? (r.CodesNum == null ? null : r.CodesNum.Value) : cpa.CodesNum.Value,
                    SetNum = cpa.SetNum == null ? (r.SetNum == null ? null : r.SetNum.Value) : cpa.SetNum.Value,
                    PeriodsNum = cpa.PeriodsNum == null ? (r.PeriodsNum == null ? null : r.PeriodsNum.Value) : cpa.PeriodsNum.Value,
                    ContractProductinfoPrice = cpa.ContractProductinfoPrice == null ? (r.ContractProductinfoPrice == null ? 0 : r.ContractProductinfoPrice) : cpa.ContractProductinfoPrice,
                    ProductPrice = cpa.ProductPrice == null ? (r.ProductPrice == null ? "" : r.ProductPrice) : cpa.ProductPrice,
                    Remark = p.Remark
                }).ToList();

            string EmptyParentProductId = Guid.Empty.ToString();
            List<ProductInfoAudit_Out> ParentProductInfoList = list
                .Where(r => r.ParentProductId != null && r.ParentProductId != EmptyParentProductId)
                .GroupBy(r => new { r.ParentProductId, r.ParentProductName, r.ParentProductPrice, r.ParentProductPriceId, r.ParentContractProductinfoPrice, r.ParentContractProductinfoPriceTotal, r.ContractId, r.Currency, r.ServiceCycleStart, r.ServiceCycleEnd, r.ChargingParameters, r.CreateUser, r.PreOpeningMonths, r.PreFirstOpeningMonths, r.PreOpeningYears })
                .Select(r => new ProductInfoAudit_Out
                {
                    Id = EmptyParentProductId,
                    ContractId = r.Key.ContractId,
                    ProductId = r.Key.ParentProductId,
                    ProductName = r.Key.ParentProductName,
                    ProductPrice = r.Key.ParentProductPrice,
                    ProductPriceId = r.Key.ParentProductPriceId,
                    ContractProductinfoPrice = r.Key.ParentContractProductinfoPrice,
                    ContractProductinfoPriceTotal = r.Key.ParentContractProductinfoPriceTotal,
                    PreOpeningMonths = r.Key.PreOpeningMonths,
                    PreFirstOpeningMonths = r.Key.PreFirstOpeningMonths,
                    PreOpeningYears = r.Key.PreOpeningYears,
                    ServiceCycle = r.Max(r => r.ServiceCycle),//r.Key.ServiceCycle
                    Currency = r.Key.Currency,
                    ServiceCycleStart = r.Key.ServiceCycleStart,
                    ServiceCycleEnd = r.Key.ServiceCycleEnd,
                    ProductType = 2,
                    Price = r.Key.ParentContractProductinfoPrice,
                    ChargingParameters = r.Key.ChargingParameters,
                    CreateUser = r.Key.CreateUser,
                    OpeningMonths = r.Max(r => r.OpeningMonths),//r.Key.OpeningMonths.Value,
                    FirstOpeningMonths = r.Max(r => r.FirstOpeningMonths),//r.Key.FirstOpeningMonths.Value,
                    OpeningYears = r.Max(r => r.OpeningYears),//r.Key.OpeningYears.Value,
                    ParentProductId = Guid.Empty.ToString(),
                    ParentProductPriceId = Guid.Empty.ToString(),
                    SubAccountsNum = r.Max(r => r.SubAccountsNum),
                    SubAccountsProductPriceId = Guid.Empty.ToString()
                })
                .ToList();
            foreach (ProductInfoAudit_Out ParentProductInfo in ParentProductInfoList)
            {
                List<ProductInfo_Out> ChildNode = list
                    .Where(r => r.ParentProductId == ParentProductInfo.ProductId)
                    .Select(r => new ProductInfo_Out
                    {
                        SubAccountsProductPrice = r.SubAccountsProductPrice,
                        SubAccountsProductPriceId = r.SubAccountsProductPriceId,
                        Id = r.Id,
                        ContractId = r.ContractId,
                        ProductId = r.ProductId,
                        ProductName = r.ProductName,
                        ProductNameEN = r.ProductNameEN,
                        ProductDescription = r.ProductDescription,
                        ProductDescriptionEn = r.ProductDescriptionEn,
                        Currency = r.Currency,
                        Price = r.Price,
                        ChargingParameters = r.ChargingParameters,
                        ProductType = r.ProductType,
                        OpeningMonths = r.OpeningMonths,
                        FirstOpeningMonths = r.FirstOpeningMonths,
                        OpeningYears = r.OpeningYears,
                        Countrys = r.Countrys,
                        PrimaryAccountsNum = r.PrimaryAccountsNum,
                        SubAccountsNum = r.SubAccountsNum,
                        CodesNum = r.CodesNum,
                        SetNum = r.SetNum,
                        PeriodsNum = r.PeriodsNum,
                        CreateUser = r.CreateUser,
                        CreateDate = r.CreateDate,
                        ProductServiceInfoApplId = r.ProductServiceInfoApplId,
                        ProductServiceInfoApplState = r.ProductServiceInfoApplState,
                        ProductServiceInfoApplProcessingType = r.ProductServiceInfoApplProcessingType,
                        ServiceCycle = r.ServiceCycle,
                        ServiceCycleStart = r.ServiceCycleStart,
                        ServiceCycleEnd = r.ServiceCycleEnd,
                        ProductPrice = r.ProductPrice,
                        ProductPriceId = r.ProductPriceId,
                        ContractProductinfoPrice = r.ContractProductinfoPrice,
                        ContractProductinfoPriceTotal = r.ContractProductinfoPriceTotal,
                        ParentProductId = r.ParentProductId,
                        ParentProductName = r.ParentProductName,
                        ParentProductPrice = r.ParentProductPrice,
                        ParentProductPriceId = r.ParentProductPriceId,
                        ParentContractProductinfoPrice = r.ParentContractProductinfoPrice,
                        ParentContractProductinfoPriceTotal = r.ParentContractProductinfoPriceTotal
                    }).ToList();
                ParentProductInfo.ChildNode = ChildNode;
            }

            List<ProductInfoAudit_Out> ProductInfo = list.Where(r => r.ParentProductId == null || r.ParentProductId == EmptyParentProductId).ToList();
            List<ProductInfoAudit_Out> result = new List<ProductInfoAudit_Out>();
            result.AddRange(ProductInfo);
            result.AddRange(ParentProductInfoList);
            foreach (ProductInfoAudit_Out p in result)
            {
                if (p.ProductType == EnumProductType.Vip.ToInt())
                {
                    List<int> countrys = p.Countrys.Split(",").Select(s => Convert.ToInt32(s)).ToList();
                    List<Db_sys_g4_dbnames> countrysList = DbOpe_sys_g4_dbnames.Instance.GetDataList(r => countrys.Contains(r.SID.Value)).ToList();
                    if (countrysList.Count > 0)
                    {
                        p.CountryNames = countrysList.Select(r => r.CountryName).JoinToString(",");
                    }

                }
            }
            return result;
        }

        public bool IsHaveContractProductinfoByContractIdAndProductType(string contractId, int productType)
        {
            return Queryable
                .LeftJoin<Db_crm_product>((r, p) => r.ProductId == p.Id)// && p.Deleted == false)
                .Where((r, p) => r.ContractId == contractId && r.Deleted == false && p.ProductType == productType)
                .Any();
        }

        public bool IsHaveContractProductServiceInfoByContractId(string contractId)
        {
            int Pass = EnumProcessStatus.Pass.ToInt();

            /*var ret = Queryable
                .LeftJoin<Db_v_productserviceinfo>((r, v) => r.Id == v.Id)
                .Where((r, v) => r.ContractId == contractId && r.Deleted == false && v.ApplState == Pass && v.ProductType != (int)EnumProductType.Periodicals)//&& v.applId != null)
                .Any();
            Db.Queryable<Db_crm_contract_serviceinfo_mailing>()
                .Where(e => e.ContractId == contractId)
                .Where(e => e.Deleted == false)
                .Any();
            return Queryable
               .LeftJoin<Db_v_productserviceinfo>((r, v) => r.Id == v.Id)
               .Where((r, v) => r.ContractId == contractId && r.Deleted == false && v.ApplState == Pass)//&& v.applId != null)
               .Any();*/

            //开通状态的期刊以外的服务
            var havePassServWithoutProject = Db.Queryable<Db_crm_contract_productinfo>()
                      .LeftJoin<Db_v_productserviceinfo>((r, v) => r.Id == v.Id)
                      .Where((r, v) => r.ContractId == contractId && r.Deleted == false && v.ApplState == Pass && v.ProductType != (int)EnumProductType.Periodicals)
                      .Any();
            //期刊邮寄的记录
            var haveMailed = Db.Queryable<Db_crm_contract_serviceinfo_mailing>()
                .Where(e => e.ContractId == contractId)
                .Where(e => e.Deleted == false)
                .Any();
            //存在：开通状态的期刊以外的服务 或者 期刊邮寄的记录
            return havePassServWithoutProject || haveMailed;
        }
        /// <summary>
        /// 客户数据-合同统计
        /// </summary>
        /// <param name="salesDataStatistics"></param>
        /// <returns></returns>
        public SalesContractStatistics_OUT SalesContractStatistics(SalesDataStatisticsParams salesDataStatistics)
        {
            SalesContractStatistics_OUT salesContractStatistics_OUT = new SalesContractStatistics_OUT();
            var queryableLeft = GetStatisticsDateSpanQuery(salesDataStatistics);
            var query = Queryable
                .LeftJoin<Db_crm_contract>((cp, contract) => contract.Id == cp.ContractId && contract.Deleted == false)
                .LeftJoin<Db_sys_user>((cp, contract, user) => contract.Issuer == user.Id && user.Deleted == false)
                .LeftJoin<Db_crm_customer_subcompany>((cp, contract, user, company) => contract.FirstParty == company.Id && company.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_customer>((cp, contract, user, company, customer) => company.CustomerId == customer.Id && customer.Deleted == (int)EnumCustomerDel.NotDel)
                .LeftJoin<Db_crm_contract_audit>((cp, contract, user, company, customer, audit) => audit.ContractId == contract.Id && audit.Deleted == false && audit.IsHistory == false)
                .LeftJoin<Db_crm_product>((cp, contract, user, company, customer, audit, product) => cp.ProductId == product.Id)// && product.Deleted == false)
                .Where((cp, contract, user, company, customer, audit, product) => contract.ContractStatus == (int)EnumContractStatus.Pass)
                .Where((cp, contract, user, company, customer, audit, product) => audit.State == (int)EnumContractStatus.Pass)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.CityIds), (cp, contract, user, company, customer, audit, product) => SqlFunc.ContainsArray(salesDataStatistics.CityIds, company.City))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.CustomerDataSources), (cp, contract, user, company, customer, audit, product) => SqlFunc.ContainsArray(salesDataStatistics.CustomerDataSources, customer.CustomerSource))
                .WhereIF(!StringUtil.IsNullOrEmpty(salesDataStatistics.UserId), (cp, contract, user, company, customer, audit, product) => salesDataStatistics.UserId == contract.Issuer)
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgDivisionId), (cp, contract, user, company, customer, audit, product) => SqlFunc.ContainsArray(salesDataStatistics.OrgDivisionId, contract.OrgDivisionId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgBrigadeId), (cp, contract, user, company, customer, audit, product) => SqlFunc.ContainsArray(salesDataStatistics.OrgBrigadeId, contract.OrgBrigadeId))
                .WhereIF(!ArrayUtil.IsNullOrEmpty(salesDataStatistics.OrgRegimentId), (cp, contract, user, company, customer, audit, product) => SqlFunc.ContainsArray(salesDataStatistics.OrgRegimentId, contract.OrgRegimentId))
                .WhereIF(salesDataStatistics.DateStart != null, (cp, contract, user, company, customer, audit, product) => audit.ReviewerDate >= salesDataStatistics.DateStart)
                .WhereIF(salesDataStatistics.DateEnd != null, (cp, contract, user, company, customer, audit, product) => audit.ReviewerDate <= salesDataStatistics.DateEnd)
                .Select((cp, contract, user, company, customer, audit, product) => new
                {
                    DateTime = SqlFunc.ToDate(audit.ReviewerDate.Value.ToString("yyyy-MM-dd")),
                    ProductKey = product.Id,
                    ProductName = cp.ProductName,
                    ContractId = cp.ContractId,
                    ContractType = contract.ContractType
                })
                .MergeTable()
                .LeftJoin(queryableLeft, (q, left) => q.DateTime >= left.StartDate && q.DateTime <= left.EndDate)
                .Select((q, left) => new
                {
                    DateTime = q.DateTime,
                    ProductKey = q.ProductKey,
                    ProductName = q.ProductName,
                    ContractId = q.ContractId,
                    ContractType = q.ContractType,
                    Index = left.Index,
                }).MergeTable();
            var r = Db.Queryable(query).OrderByDescending(q => q.Index).ToList();
            var newR = Db.Queryable(query)
                .Where(q => q.ContractType == (int)EnumContractType.New)
                .GroupBy(q => q.DateTime)
                .Select(q => new
                {
                    DateTime = q.DateTime,
                    Count = SqlFunc.AggregateCount(q.ContractId)
                })
                .MergeTable()
                .RightJoin(queryableLeft, (q, left) => q.DateTime >= left.StartDate && q.DateTime <= left.EndDate)
                .Select((q, left) => new SalesContractStatisticsItem_OUT
                {
                    Index = left.Index,
                    Key = left.Key,
                    Count = SqlFunc.IsNullOrEmpty(q.Count) ? 0 : q.Count,
                })
                .Mapper(it =>
                {
                    it.Name = EnumContractType.New.GetEnumDescription();
                })
                .ToList();
            Db.ThenMapper(newR, item =>
            {
                item.Products = Db.Queryable(query)
                .Where(q => q.ContractType == (int)EnumContractType.New)
                .GroupBy(q => new { q.Index, q.ProductKey, q.ProductName })
                .Select(q => new SalesContractStatisticsProductItem_OUT() { Index = q.Index, Key = q.ProductKey, Name = q.ProductName, Count = SqlFunc.AggregateCount(q.ProductKey) })
                .MergeTable()
                .SetContext(x => x.Index, () => item.Index, item).ToList();
            });
            var renewR = Db.Queryable(query)
            .Where(q => q.ContractType == (int)EnumContractType.ReNew)
            .GroupBy(q => q.DateTime)
            .Select(q => new
            {
                DateTime = q.DateTime,
                Count = SqlFunc.AggregateCount(q.ContractId)
            })
            .MergeTable()
            .RightJoin(queryableLeft, (q, left) => q.DateTime >= left.StartDate && q.DateTime <= left.EndDate)
            .Select((q, left) => new SalesContractStatisticsItem_OUT
            {
                Index = left.Index,
                Key = left.Key,
                Count = SqlFunc.IsNullOrEmpty(q.Count) ? 0 : q.Count
            })
            .Mapper(it =>
            {
                it.Name = EnumContractType.ReNew.GetEnumDescription();
            })
            .ToList();
            Db.ThenMapper(renewR, item =>
            {
                item.Products = Db.Queryable(query)
                .Where(q => q.ContractType == (int)EnumContractType.ReNew)
                .GroupBy(q => new { q.Index, q.ProductKey, q.ProductName })
                .Select(it => new SalesContractStatisticsProductItem_OUT() { Index = it.Index, Key = it.ProductKey, Name = it.ProductName, Count = SqlFunc.AggregateCount(it.ProductKey) })
                .MergeTable()
                .SetContext(x => x.Index, () => item.Index, item).ToList();
            });
            var addR = Db.Queryable(query)
            .Where(q => q.ContractType == (int)EnumContractType.AddItem)
            .GroupBy(q => q.DateTime)
            .Select(q => new
            {
                DateTime = q.DateTime,
                Count = SqlFunc.AggregateCount(q.ContractId)
            })
            .MergeTable()
            .RightJoin(queryableLeft, (q, left) => q.DateTime >= left.StartDate && q.DateTime <= left.EndDate)
            .Select((q, left) => new SalesContractStatisticsItem_OUT
            {
                Index = left.Index,
                Key = left.Key,
                Count = SqlFunc.IsNullOrEmpty(q.Count) ? 0 : q.Count
            })
            .Mapper(it =>
            {
                it.Name = EnumContractType.AddItem.GetEnumDescription();
            })
            .ToList();
            Db.ThenMapper(addR, item =>
            {
                item.Products = Db.Queryable(query)
                .Where(q => q.ContractType == (int)EnumContractType.AddItem)
                .GroupBy(q => new { q.Index, q.ProductKey, q.ProductName })
                .Select(it => new SalesContractStatisticsProductItem_OUT() { Index = it.Index, Key = it.ProductKey, Name = it.ProductName, Count = SqlFunc.AggregateCount(it.ProductKey) })
                .MergeTable()
                .SetContext(x => x.Index, () => item.Index, item).ToList();
            });
            salesContractStatistics_OUT.AddItemItems = addR;
            salesContractStatistics_OUT.NewItems = newR;
            salesContractStatistics_OUT.ReNewItems = renewR;
            return salesContractStatistics_OUT;
        }


        /// <summary>
        /// 根据合同Id，判断该合同是否有已开通的GTIS服务
        /// </summary>
        /// <param name="ContractId"></param>
        /// <returns></returns>
        public EnumContractGtisProductServiceState CheckHasUnValidGTISService(string ContractId)
        {
            var productList = Queryable.LeftJoin<Db_crm_product>((cp, p) => cp.ProductId == p.Id)
                .LeftJoin<Db_crm_contract_productserviceinfo_gtis_appl>((cp, p, pga) => pga.ContractId == cp.ContractId && pga.ProductId == p.Id && pga.Deleted == false && pga.IsInvalid == 0 && pga.ContractProductInfoId == cp.Id)
                .LeftJoin<Db_crm_contract_serviceinfo_gtis>((cp, p, pga, sg) => sg.ContractId == cp.ContractId && sg.ProductId == p.Id && sg.ProductServiceInfoGtisApplId == pga.Id && sg.Deleted == false && sg.ContractProductInfoId == cp.Id && sg.IsChanged == 0 && sg.IsApplHistory == false && (sg.State == 0 || sg.State == 1 || sg.State == 2))
                .Where((cp, p) => p.ProductType == (int)EnumProductType.Gtis || p.ProductType == (int)EnumProductType.Vip)
                .Where(cp => cp.ContractId == ContractId)
                .Where(cp => cp.Deleted == false)
                .Select((cp, p, pga, sg) => new Db_v_productserviceinfo
                {
                    Id = cp.Id,
                    ContractId = cp.ContractId,
                    ProductType = p.ProductType,
                    applId = pga.Id,
                    ApplState = pga.State,
                    ProcessingType = pga.ProcessingType,
                    ServiceState = sg.State,
                    ServiceCycleEnd = sg.ServiceCycleEnd,
                })
                .ToList();

            /*var productList = Db.Queryable<Db_v_productserviceinfo>()
                .Where(e => e.ContractId.Equals(ContractId))
                .Where(e => e.ProductType == (int)EnumProductType.Gtis || e.ProductType == (int)EnumProductType.Vip)
                .ToList();*/
            if (productList.Count == 0)
                return EnumContractGtisProductServiceState.Inexistence;
            else if (productList.Any(e => e.ServiceState == (long)EnumContractServiceState.VALID || e.ServiceState == (long)EnumContractServiceState.OUT))
                return EnumContractGtisProductServiceState.Opened;
            else
                return EnumContractGtisProductServiceState.ExistAndNotopen;
        }


        /// <summary>
        /// 查验当前合同是否存在环球搜产品
        /// </summary>
        /// <param name="ContractId"></param>
        /// <returns></returns>
        public bool CheckContractHasGlobalSearchProduct(string ContractId)
        {
            return Queryable.LeftJoin<Db_crm_product>((cp, p) => cp.ProductId == p.Id)
                .LeftJoin<Db_crm_contract_productserviceinfo_globalsearch_appl>((cp, p, pga) => pga.ContractId == cp.ContractId && pga.ProductId == p.Id && pga.Deleted == false && pga.IsInvalid == 0 && pga.ContractProductInfoId == cp.Id)
                .LeftJoin<Db_crm_contract_serviceinfo_globalsearch>((cp, p, pga, sg) => sg.ContractId == cp.ContractId && sg.ProductId == p.Id && sg.ProductServiceInfoGlobalSearchApplId == pga.Id && sg.Deleted == false && sg.ContractProductInfoId == cp.Id && sg.IsChanged == false && sg.IsHistory == false && (sg.State == EnumContractServiceState.INVALID || sg.State == EnumContractServiceState.VALID || sg.State == EnumContractServiceState.OUT))
                .Where((cp, p) => p.ProductType == (int)EnumProductType.Global)
                .Where(cp => cp.ContractId == ContractId)
                .Where(cp => cp.Deleted == false)
                .Any();

        }

        /// <summary>
        /// 查验当前合同是否存在环球搜产品
        /// </summary>
        /// <param name="ContractId"></param>
        /// <param name="productId"></param>
        /// <param name="conProductId"></param>
        /// <returns></returns>
        public bool CheckContractHasGlobalSearchProduct(string ContractId, ref string productId, ref string conProductId)
        {
            var obj = Queryable.LeftJoin<Db_crm_product>((cp, p) => cp.ProductId == p.Id)
                .Where((cp, p) => p.ProductType == (int)EnumProductType.Global)
                .Where(cp => cp.ContractId == ContractId)
                .Where(cp => cp.Deleted == false)
                .Select(cp => new Db_crm_contract_productinfo()
                {
                    Id = cp.Id.SelectAll(),
                })
                .First();
            if (obj != null)
            {
                productId = obj.ProductId;
                conProductId = obj.Id;
                return true;
            }
            else
                return false;

        }

        /// <summary>
        /// 查验当前合同是否存在慧思学院产品
        /// </summary>
        /// <param name="ContractId"></param>
        /// <returns></returns>
        public bool CheckContractHasCollegeProduct(string ContractId)
        {
            return Queryable.LeftJoin<Db_crm_product>((cp, p) => cp.ProductId == p.Id)
                .LeftJoin<Db_crm_contract_productserviceinfo_college_appl>((cp, p, pca) => pca.ContractId == cp.ContractId && pca.ProductId == p.Id && pca.Deleted == false && pca.IsInvalid == 0 && pca.ContractProductInfoId == cp.Id)
                .LeftJoin<Db_crm_contract_serviceinfo_college>((cp, p, pca, sc) => sc.ContractId == cp.ContractId && sc.ProductId == p.Id && sc.ProductServiceInfoCollegeApplId == pca.Id && sc.Deleted == false && sc.ContractProductInfoId == cp.Id && sc.IsChanged == false && sc.IsHistory == false && (sc.State == EnumContractServiceState.INVALID || sc.State == EnumContractServiceState.VALID || sc.State == EnumContractServiceState.OUT))
                .Where((cp, p) => p.ProductType == (int)EnumProductType.GlobalWitsSchool)
                .Where(cp => cp.ContractId == ContractId)
                .Where(cp => cp.Deleted == false)
                .Any();

            /*return Db.Queryable<Db_v_productserviceinfo>()
                .Where(e => e.ContractId == ContractId)
                .Where(e => e.ProductType == (int)EnumProductType.GlobalWitsSchool)
                .Any();*/
        }

        public Db_crm_contract_productinfo GetContractProductByContractId(string ContractId)
        {
            return Queryable
                .LeftJoin<Db_crm_product>((cp, p) => cp.ProductId == p.Id)
                .Where(cp => cp.ContractId == ContractId)
                .Where((cp, p) => cp.Deleted == false && p.Deleted == false)
                .Where((cp, p) => p.ProductType == (int)EnumProductType.Gtis || p.ProductType == (int)EnumProductType.Vip)
                .First();
        }

        /// <summary>
        /// 查验当前合同是否存在Vip零售国家
        /// </summary>
        /// <param name="ContractId"></param>
        /// <returns></returns>
        public bool CheckContractHasVIP(string ContractId)
        {
            return Queryable
                .LeftJoin<Db_crm_product>((cp, p) => cp.ProductId == p.Id)
                .Where((cp, p) => p.ProductType == (int)EnumProductType.Vip)
                .Where(cp => cp.ContractId == ContractId)
                .Where(cp => cp.Deleted == false)
                .Any();
        }

        /// <summary>
        ///  查验当前合同是否存在Gtis专业版
        /// </summary>
        /// <param name="ContractId"></param>
        /// <returns></returns>
        public bool CheckContractHasGtisProfessional(string ContractId)
        {
            var professionalId = new List<string>() { "761c3f51-afd0-4087-8cd3-9f9db8225cad", "716c3f51-afd0-4087-8cd3-9f9db8225cad" };
            return Queryable
                .LeftJoin<Db_crm_product>((cp, p) => cp.ProductId == p.Id)
                .Where((cp, p) => professionalId.Contains(p.Id))
                .Where(cp => cp.ContractId == ContractId)
                .Where(cp => cp.Deleted == false)
                .Any();
        }

        /// <summary>
        /// 增量合同获取主合同及其他子合同产品
        /// </summary>
        /// <param name="parentContractId"></param>
        /// <param name="contractId"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public List<ProductInfo_In> GetIncreaseContractProduct(string parentContractId, string contractId = null)
        {
            return Queryable.LeftJoin<Db_crm_contract>((p, c) => p.ContractId == c.Id && p.Deleted == false)
                   .LeftJoin<Db_crm_product>((p, c, cp) => cp.Id == p.ProductId)
                   .Where((p, c) => c.Id == parentContractId || c.ParentContractId == parentContractId)
                   .WhereIF(contractId.IsNotNullOrEmpty(), (p, c) => c.Id != contractId)
                   .Where((p, c) => c.ContractStatus == (int)EnumContractStatus.Pass || c.ContractStatus == (int)EnumContractStatus.AutoPass)
                   .Select((p, c, cp) => new ProductInfo_In
                   {
                       ProductType = (EnumProductType)cp.ProductType.Value,
                   }, true).ToList();
        }

        /// <summary>
        /// 查看当前合同是否可以申请服务
        /// </summary>
        /// <param name="contractId"></param>
        /// <param name="dataOwner"></param>
        /// <returns></returns>
        public void CheckContractCanApply(string contractId, bool dataOwner = false)
        {
            var contractInfo = Db.Queryable<Db_crm_contract>().Where(e => e.Id == contractId).First();
            if(contractInfo.ContractType == (int)EnumContractType.AddItem)
                throw new ApiException("当前合同为增项合同，无法进行服务申请");
            //验证邓白氏、其他数据、期刊 类型的产品是否可以申请服务
            var retBool_1 = Queryable
                .LeftJoin<Db_v_productserviceinfo>((p, vp) => p.Id == vp.Id)
                .LeftJoin<Db_crm_contract>((p, vp, con) => vp.ContractId == con.Id && con.Deleted == false)
                .LeftJoin<Db_v_customer_subcompany_private_user>((p, vp, con, cspu) => cspu.Id == con.FirstParty)
                .Where((p, vp) => vp.ContractId == contractId)
                .Where((p, vp) => vp.ProductType == (int)EnumProductType.DandB || vp.ProductType == (int)EnumProductType.Other || vp.ProductType == (int)EnumProductType.Periodicals)
                .Where((p, vp) => vp.ApplState == null || (vp.ProcessingType == (int)EnumProcessingType.Add && vp.ApplState == (int)EnumProcessStatus.Refuse))
                .WhereIF(dataOwner, DataOwner.BusinessData("p.CreateUser", "p.ContractId", "cspu.CompanyCurrentUser"))
                .Any();
            //验证慧思产品是否可以申请服务
            var retBool_2 = !Db.Queryable<Db_crm_contract_productserviceinfo_wits_appl>()
                .Where(e => e.ContractId == contractId)
                .Where(e => e.IsInvalid == EnumIsInvalid.Effective && !(e.ProcessingType == EnumProcessingType.Add && e.State == EnumProcessStatus.Refuse) && e.State != EnumProcessStatus.Void)
                .Any();

            if (!retBool_1 && !retBool_2)
                throw new ApiException("当前合同的全部产品服务均已申请，无法再进行申请服务");

        }


        /// <summary>
        /// 根据合同Id获取合同的待申请产品列表
        /// 新版，原版是 GetContractProductInfoByContractId
        /// </summary>
        /// <param name="contractId"></param>
        /// <param name="dataOwner"></param>
        /// <returns></returns>
        public List<ProductInfo4ServeApply> GetProductInfoByContractId4ServeApply(string contractId, bool dataOwner = false)
        {
            int Void = EnumProcessStatus.Void.ToInt();
            int Submit = EnumProcessStatus.Submit.ToInt();
            int Pass = EnumProcessStatus.Pass.ToInt();
            return Queryable
                .LeftJoin<Db_crm_product>((r, p) => r.ProductId == p.Id)//&& p.Deleted == false)
                .LeftJoin<Db_crm_contract>((r, p, c) => r.ContractId == c.Id && c.Deleted == false)
                .LeftJoin<Db_crm_product_price>((r, p, c, pp) => r.ProductPriceId == pp.Id && pp.Deleted == false)
                .LeftJoin<Db_v_productserviceinfo>((r, p, c, pp, vp) => r.Id == vp.Id && (vp.ApplState == Submit || vp.ApplState == Pass))//vp.ApplState != Void &&
                .LeftJoin<Db_v_customer_subcompany_private_user>((r, p, c, pp, vp, cspu) => cspu.Id == c.FirstParty)
                .Where((r, p, c, pp, vp, cspu) => r.ContractId == contractId && r.Deleted == false)
                .WhereIF(dataOwner, DataOwner.BusinessData("r.CreateUser", "r.ContractId", "cspu.CompanyCurrentUser"))
                .Select((r, p, c, pp, vp, cspu) => new ProductInfo4ServeApply
                {
                    Id = r.Id.SelectAll(),
                    //ProductName = p.ProductName,
                    ProductNameEN = p.ProductNameEN,
                    ProductNum = p.ProductNum,
                    ProductDescription = p.ProductDescription,
                    ProductDescriptionEn = p.ProductDescriptionEn,
                    ProductType = (EnumProductType)p.ProductType.Value,
                    Currency = c.Currency.Value,
                    Price = pp.Price,
                    ChargingParameters = pp.ChargingParameters,
                    ProductServiceInfoApplId = vp.applId,
                    ProductServiceInfoApplState = vp.ApplState,
                    ProductServiceInfoApplProcessingType = vp.ProcessingType,
                    ServiceCycle = r.OpeningYears,//pmy.Year,//p.ServiceCycle,
                    ServiceCycleStart = p.ServiceCycleStart,
                    ServiceCycleEnd = p.ServiceCycleEnd,
                    Remark = p.Remark,
                    NotForSale = p.NotForSale,
                    ServiceEndDate = vp.ServiceCycleEnd,
                    ServiceStartDate = vp.ServiceCycleStart,
                    OpenServiceCycle = (vp.ServiceCycleStart == null || vp.ServiceCycleEnd == null) ? "--" : vp.ServiceCycleStart.Value.ToString("yyyy-MM-dd") + "至" + vp.ServiceCycleEnd.Value.ToString("yyyy-MM-dd"),
                    ServiceState = vp.ServiceState,
                    ServiceRemark = vp.Remark,
                    Countries = r.Countrys,
                    ContractProductInfoSeriesId = r.SeriesId,
                }, true).ToList();
        }

        /// <summary>
        /// 根据siriesId获取合同产品信息
        /// </summary>
        /// <param name="seriesId"></param>
        public List<GetContractProductInfoBySeiresId_Out> GetContractProductInfoBySeiresId(string seriesId)
        {
            return Queryable
                .LeftJoin<Db_crm_product>((e, f) => e.ProductId == f.Id && f.Deleted == false)
                .Where(e => e.SeriesId == seriesId)
                .Where(e => e.Deleted == false)
                .Select((e, f) => new GetContractProductInfoBySeiresId_Out()
                {
                    Id = e.Id.SelectAll(),
                    ProductType = (EnumProductType)f.ProductType
                })
                .ToList();
        }



        /// <summary>
        /// 查找旧合同SeriesId
        /// </summary>
        /// <param name="contractId"></param>
        /// <returns></returns>
        public string GetMainContractSeriesId(string contractId)
        {
            List<int> SeriesType = new List<int> { (int)EnumProductType.DandB, (int)EnumProductType.Event, (int)EnumProductType.Other, (int)EnumProductType.Periodicals };

            return Queryable.LeftJoin<Db_crm_product>((c, p) => c.ProductId == p.Id)
                            .Where((c, p) => c.ContractId == contractId)
                            .Where((c, p) => !SeriesType.Contains(p.ProductType.Value))
                            .Select((c, p) => c.SeriesId).First();
        }

        /// <summary>
        /// 回写主合同SeriesId
        /// </summary>
        /// <param name="parentContractId"></param>
        /// <param name="newSeriesId"></param>
        /// <exception cref="NotImplementedException"></exception>
        public void UpdateMainContractSeriesId(string parentContractId, string newSeriesId)
        {
            List<int> SeriesType = new List<int> { (int)EnumProductType.DandB, (int)EnumProductType.Event, (int)EnumProductType.Other, (int)EnumProductType.Periodicals };

            var OldProductInfo = Queryable.LeftJoin<Db_crm_product>((c, p) => c.ProductId == p.Id)
                            .Where((c, p) => c.ContractId == parentContractId)
                            .Select((c, p) => new
                            {
                                productInfoId = c.Id,
                                seriesId = c.SeriesId,
                                productId = c.ProductId,
                                productType = p.ProductType,
                            }).ToList();
            OldProductInfo.ForEach(it =>
            {
                if (SeriesType.Contains(it.productType.Value))
                {
                    string newid = new Guid().ToString();
                    Updateable.SetColumns(r => r.SeriesId == newid).Where(r => r.Id == it.productInfoId).AddQueue();
                }
                else
                {
                    Updateable.SetColumns(r => r.SeriesId == newSeriesId).Where(r => r.Id == it.productInfoId).AddQueue();
                }
            });
            Instance.SaveQueues();

        }

        /// <summary>
        /// 在服务申请时，补充2025.7月 SalesWits产品加入前的合同数据的SeiresId
        /// </summary>
        /// <param name="id"></param>
        /// <param name="seriesId"></param>
        public void UpdateSeriesIdBeforSalesWits(string id, string seriesId)
        {
            Updateable
                .SetColumns(e => e.SeriesId == seriesId)
                .Where(e => e.Id == id)
                .ExecuteCommand();
        }
    }
}
