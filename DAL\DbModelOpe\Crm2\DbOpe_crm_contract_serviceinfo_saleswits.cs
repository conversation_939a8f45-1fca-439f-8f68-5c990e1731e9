using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_contract_serviceinfo_saleswits表操作
    /// </summary>
    public class DbOpe_crm_contract_serviceinfo_saleswits:DbOperateCrm2Ex<Db_crm_contract_serviceinfo_saleswits,DbOpe_crm_contract_serviceinfo_saleswits>
    {

        /// <summary>
        /// 根据申请Id获取当前生效的服务数据
        /// </summary>
        /// <param name="ApplyId"></param>
        /// <returns></returns>
        public Db_crm_contract_serviceinfo_saleswits GetSerivceDataByApplyId(string ApplyId)
        {
            return Queryable
                .Where(e => e.ProductServiceInfoSalesWitsApplId == ApplyId)
                .Where(e => e.Deleted == false && e.IsChanged == false && e.IsHistory == false)
                .First();
        }

        /// <summary>
        /// 根据Wits申请ID获取SalesWits服务数据
        /// </summary>
        /// <param name="witsApplId"></param>
        public GetWitsApplyInfo4Audit_Out_RegisterInfo_SalesWits GetServiceInfoByWitsApplyId(string witsApplId)
        {
            return Queryable
                .LeftJoin<Db_crm_product>((e, f) => e.ProductId == f.Id && f.Deleted == false)
                .Where(e => e.WitsApplId == witsApplId)
                .Where(e => e.IsHistory == false && e.IsChanged == false && e.Deleted == false)
                .Select((e, f) => new GetWitsApplyInfo4Audit_Out_RegisterInfo_SalesWits
                {
                    Id = e.ProductServiceInfoSalesWitsApplId,
                    ProducttId = e.ProductId,
                    AccountNum = e.AccountsNum.Value,
                    ServiceCycleStart = e.ServiceCycleStart.Value,
                    ServiceCycleEnd = e.ServiceCycleEnd.Value,
                    ServiceMonth = e.ServiceMonth.Value,
                    ProductName = f.ProductName,
                    Remark = e.Remark,
                    AddCredit = e.RechargeAmount.Value,
                    GiftResourceMonths = e.CurrentGiftMonths.Value,
                    TokenCount = e.CurrentGiftTokens.Value,
                    EmailCount = e.CurrentGiftEmails.Value,
                })
                .First();
        }

        /// <summary>
        /// 获取所有曾经开通过的saleswits服务数据
        /// </summary>
        /// <param name="contractNum"></param>
        /// <returns></returns>
        public Db_crm_contract_serviceinfo_saleswits GetOnceOpendServiceByContractNum(string contractNum)
        {
            return Queryable
                .Where(e => e.ContractNum == contractNum)
                .Where(e => e.Deleted == false && e.IsHistory == false && e.IsChanged == false)
                .Where(e => e.State == EnumContractServiceState.VALID || e.State == EnumContractServiceState.OUT || e.State == EnumContractServiceState.INVALID)
                .OrderByDescending(e => e.ReviewerTime)
                .First();
        }
    }
}
