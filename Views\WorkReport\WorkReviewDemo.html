<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作回顾模块演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            background-color: #f5f5f5;
            color: #333;
            font-size: 14px;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .demo-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .demo-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .feature-desc {
            color: #666;
            line-height: 1.6;
        }
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .demo-table th,
        .demo-table td {
            border: 1px solid #ebeef5;
            padding: 12px;
            text-align: center;
        }
        .demo-table th {
            background-color: #f5f7fa;
            font-weight: 500;
            color: #606266;
        }
        .demo-table tbody tr:hover {
            background-color: #f5f7fa;
        }
        .trend-up {
            color: #67c23a;
            font-weight: bold;
        }
        .trend-down {
            color: #f56c6c;
            font-weight: bold;
        }
        .number-cell {
            font-weight: 500;
        }
        .demo-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .demo-btn {
            display: inline-block;
            padding: 12px 30px;
            margin: 0 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .demo-btn.secondary {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        .implementation-list {
            list-style: none;
            padding: 0;
        }
        .implementation-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 30px;
        }
        .implementation-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #67c23a;
            font-weight: bold;
        }
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .tech-tag {
            background: #ecf5ff;
            color: #409eff;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            border: 1px solid #d9ecff;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 演示标题 -->
        <div class="demo-header">
            <div class="demo-title">📊 工作回顾模块原型演示</div>
            <div class="demo-subtitle">基于日报数据的团队工作量统计分析系统</div>
        </div>

        <!-- 功能特性 -->
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">📈</div>
                <div class="feature-title">汇总统计</div>
                <div class="feature-desc">
                    全面统计团队工作量指标，包括电话量、拜访量、邮件量、社媒量等12项核心指标，支持同期对比分析。
                </div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <div class="feature-title">人员详细</div>
                <div class="feature-desc">
                    按人员维度展示详细工作量数据，支持个人绩效分析和团队成员对比，便于精准管理。
                </div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🏢</div>
                <div class="feature-title">部门统计</div>
                <div class="feature-desc">
                    按部门层级统计工作量，支持直属和下属统计模式，满足不同管理层级的需求。
                </div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-title">趋势分析</div>
                <div class="feature-desc">
                    提供环比变化分析，直观显示工作量趋势，帮助管理者快速识别问题和机会。
                </div>
            </div>
        </div>

        <!-- 核心数据展示 -->
        <div class="demo-section">
            <div class="section-title">📈 汇总统计示例</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">156</div>
                    <div class="stat-label">电话量 (****%)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">45</div>
                    <div class="stat-label">拜访量 (+18.4%)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">234</div>
                    <div class="stat-label">社媒量 (+18.2%)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">25</div>
                    <div class="stat-label">新增客户 (+38.9%)</div>
                </div>
            </div>
            
            <table class="demo-table">
                <thead>
                    <tr>
                        <th>指标类型</th>
                        <th>本月数量</th>
                        <th>上月数量</th>
                        <th>环比变化</th>
                        <th>变化幅度</th>
                        <th>趋势</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>电话量</td>
                        <td class="number-cell">156</td>
                        <td class="number-cell">142</td>
                        <td class="trend-up">+14</td>
                        <td class="trend-up">****%</td>
                        <td class="trend-up">↗</td>
                    </tr>
                    <tr>
                        <td>拜访量</td>
                        <td class="number-cell">45</td>
                        <td class="number-cell">38</td>
                        <td class="trend-up">+7</td>
                        <td class="trend-up">+18.4%</td>
                        <td class="trend-up">↗</td>
                    </tr>
                    <tr>
                        <td>新增客户</td>
                        <td class="number-cell">25</td>
                        <td class="number-cell">18</td>
                        <td class="trend-up">+7</td>
                        <td class="trend-up">+38.9%</td>
                        <td class="trend-up">↗</td>
                    </tr>
                    <tr>
                        <td>签约客户</td>
                        <td class="number-cell">12</td>
                        <td class="number-cell">8</td>
                        <td class="trend-up">+4</td>
                        <td class="trend-up">+50.0%</td>
                        <td class="trend-up">↗</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 技术实现 -->
        <div class="demo-section">
            <div class="section-title">🛠️ 技术实现方案</div>
            <ul class="implementation-list">
                <li>基于设计文档完整实现所有功能模块</li>
                <li>采用模块化JavaScript架构，便于维护和扩展</li>
                <li>响应式表格设计，支持大数据量展示</li>
                <li>支持多种筛选条件和对比模式</li>
                <li>完整的数据导出功能</li>
                <li>符合现有CRM系统UI风格</li>
            </ul>
            
            <div class="tech-stack">
                <span class="tech-tag">HTML5</span>
                <span class="tech-tag">CSS3</span>
                <span class="tech-tag">JavaScript ES6+</span>
                <span class="tech-tag">响应式设计</span>
                <span class="tech-tag">模块化架构</span>
                <span class="tech-tag">数据可视化</span>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="demo-buttons">
            <button class="demo-btn" onclick="openFullDemo()">查看完整原型</button>
            <button class="demo-btn secondary" onclick="viewDocumentation()">查看设计文档</button>
        </div>

        <!-- 设计说明 -->
        <div class="demo-section">
            <div class="section-title">📋 设计说明</div>
            <p style="line-height: 1.8; color: #666;">
                本原型严格按照《工作回顾模块设计文档》和《工作回顾模块设计讨论总结》的要求设计，
                实现了基于日报数据的团队工作量统计分析功能。采用了与现有CRM系统一致的UI风格，
                支持多维度统计、同期对比、趋势分析等核心功能。表格设计简洁明了，
                数据展示直观清晰，完全满足管理者的使用需求。
            </p>
        </div>
    </div>

    <script>
        function openFullDemo() {
            // 在实际环境中，这里会打开完整的原型页面
            if (confirm('是否打开完整的工作回顾模块原型？')) {
                window.open('WorkReviewModule.html', '_blank');
            }
        }

        function viewDocumentation() {
            alert('设计文档位于：\n1. Doc/WorkReport/工作回顾模块设计文档.md\n2. Doc/WorkReport/工作回顾模块设计讨论总结.md');
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为统计卡片添加点击效果
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // 为表格行添加点击效果
            const tableRows = document.querySelectorAll('.demo-table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('click', function() {
                    // 移除其他行的选中状态
                    tableRows.forEach(r => r.style.backgroundColor = '');
                    // 高亮当前行
                    this.style.backgroundColor = '#e6f7ff';
                });
            });
        });
    </script>
</body>
</html>
