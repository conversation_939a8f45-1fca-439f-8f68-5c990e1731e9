/**
 * 工作回顾模块 - 表格相关JavaScript文件
 * 负责表格的生成、渲染和数据展示
 */

/**
 * 加载表格内容
 */
function loadTableContent(tabName) {
    const container = document.getElementById('tableContainer');
    if (!container) return;
    
    const enableComparison = document.getElementById('enableComparison').checked;
    
    switch(tabName) {
        case 'summary':
            container.innerHTML = generateSummaryTable(enableComparison);
            break;
        case 'personnel':
            container.innerHTML = generatePersonnelTable(enableComparison);
            break;
        case 'department':
            container.innerHTML = generateDepartmentTable(enableComparison);
            break;
        default:
            container.innerHTML = '<div class="no-data">未找到对应的表格内容</div>';
    }
}

/**
 * 生成汇总统计表格
 */
function generateSummaryTable(enableComparison) {
    const data = currentData?.summary;
    if (!data) return '<div class="no-data">暂无数据</div>';
    
    const indicators = [
        { key: 'phoneCount', name: '电话量' },
        { key: 'visitCount', name: '拜访量' },
        { key: 'emailPreciseCount', name: '邮件量(精发)' },
        { key: 'emailBulkCount', name: '邮件量(粗发)' },
        { key: 'socialMediaCount', name: '社媒量' },
        { key: 'replyCount', name: '回复量' },
        { key: 'demoCount', name: '演示量' },
        { key: 'newCustomerCount', name: '新增客户' },
        { key: 'followCustomerCount', name: '跟进客户' },
        { key: 'contractCustomerCount', name: '签约客户' },
        { key: 'reportCount', name: '日报总数' },
        { key: 'onTimeRate', name: '按时完成率', isPercentage: true }
    ];
    
    let tableHtml = `
        <div id="summaryTab" class="tab-content active">
            <div class="statistics-card">
                <div class="statistics-header">
                    <span>📈 汇总统计表</span>
                    <span id="summaryPeriod">${getPeriodLabel(enableComparison)}</span>
                </div>
                <div class="statistics-body">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th style="width: 120px;">指标类型</th>
                                    <th style="width: 100px;">当前数量</th>`;
    
    if (enableComparison) {
        tableHtml += `
                                    <th style="width: 100px;">对比数量</th>
                                    <th style="width: 100px;">环比变化</th>
                                    <th style="width: 100px;">变化幅度</th>
                                    <th style="width: 60px;">趋势</th>`;
    }
    
    tableHtml += `
                                </tr>
                            </thead>
                            <tbody>`;
    
    indicators.forEach(indicator => {
        const current = data.current[indicator.key];
        const previous = data.previous[indicator.key];
        const change = calculateChange(current, previous);
        
        tableHtml += `
                                <tr>
                                    <td>${indicator.name}</td>
                                    <td class="number-cell">${formatIndicatorValue(current, indicator.isPercentage)}</td>`;
        
        if (enableComparison) {
            tableHtml += `
                                    <td class="number-cell">${formatIndicatorValue(previous, indicator.isPercentage)}</td>
                                    <td class="change-cell trend-${change.trend}">${formatChangeValue(change.change, indicator.isPercentage)}</td>
                                    <td class="percentage-cell trend-${change.trend}">${formatPercentage(change.percentage)}</td>
                                    <td class="trend-${change.trend}">${getTrendIcon(change.trend)}</td>`;
        }
        
        tableHtml += `
                                </tr>`;
    });
    
    tableHtml += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>`;
    
    return tableHtml;
}

/**
 * 生成人员详细统计表格
 */
function generatePersonnelTable(enableComparison) {
    const data = currentData?.personnel;
    if (!data || data.length === 0) return '<div class="no-data">暂无人员数据</div>';
    
    let tableHtml = `
        <div id="personnelTab" class="tab-content active">
            <div class="statistics-card">
                <div class="statistics-header">
                    <span>👥 人员详细统计表</span>
                    <span id="personnelPeriod">${getPeriodLabel(enableComparison)}</span>
                </div>
                <div class="statistics-body">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th rowspan="2" style="width: 80px;">姓名</th>
                                    <th colspan="6">当前数据</th>`;
    
    if (enableComparison) {
        tableHtml += `<th colspan="6">对比数据</th>`;
    }
    
    tableHtml += `
                                </tr>
                                <tr>
                                    <th style="width: 60px;">电话量</th>
                                    <th style="width: 60px;">拜访量</th>
                                    <th style="width: 60px;">邮件量</th>
                                    <th style="width: 60px;">社媒量</th>
                                    <th style="width: 60px;">新增客户</th>
                                    <th style="width: 60px;">签约客户</th>`;
    
    if (enableComparison) {
        tableHtml += `
                                    <th style="width: 60px;">电话量</th>
                                    <th style="width: 60px;">拜访量</th>
                                    <th style="width: 60px;">邮件量</th>
                                    <th style="width: 60px;">社媒量</th>
                                    <th style="width: 60px;">新增客户</th>
                                    <th style="width: 60px;">签约客户</th>`;
    }
    
    tableHtml += `
                                </tr>
                            </thead>
                            <tbody>`;
    
    // 计算合计
    let totals = {
        current: { phoneCount: 0, visitCount: 0, emailCount: 0, socialMediaCount: 0, newCustomerCount: 0, contractCustomerCount: 0 },
        previous: { phoneCount: 0, visitCount: 0, emailCount: 0, socialMediaCount: 0, newCustomerCount: 0, contractCustomerCount: 0 }
    };
    
    data.forEach(person => {
        Object.keys(totals.current).forEach(key => {
            totals.current[key] += person.current[key] || 0;
            totals.previous[key] += person.previous[key] || 0;
        });
        
        tableHtml += `
                                <tr>
                                    <td>${person.name}</td>
                                    <td class="number-cell">${person.current.phoneCount}</td>
                                    <td class="number-cell">${person.current.visitCount}</td>
                                    <td class="number-cell">${person.current.emailCount}</td>
                                    <td class="number-cell">${person.current.socialMediaCount}</td>
                                    <td class="number-cell">${person.current.newCustomerCount}</td>
                                    <td class="number-cell">${person.current.contractCustomerCount}</td>`;
        
        if (enableComparison) {
            tableHtml += `
                                    <td class="number-cell">${person.previous.phoneCount}</td>
                                    <td class="number-cell">${person.previous.visitCount}</td>
                                    <td class="number-cell">${person.previous.emailCount}</td>
                                    <td class="number-cell">${person.previous.socialMediaCount}</td>
                                    <td class="number-cell">${person.previous.newCustomerCount}</td>
                                    <td class="number-cell">${person.previous.contractCustomerCount}</td>`;
        }
        
        tableHtml += `
                                </tr>`;
    });
    
    // 添加合计行
    tableHtml += `
                                <tr style="background-color: #f0f9ff; font-weight: bold;">
                                    <td>合计</td>
                                    <td class="number-cell">${totals.current.phoneCount}</td>
                                    <td class="number-cell">${totals.current.visitCount}</td>
                                    <td class="number-cell">${totals.current.emailCount}</td>
                                    <td class="number-cell">${totals.current.socialMediaCount}</td>
                                    <td class="number-cell">${totals.current.newCustomerCount}</td>
                                    <td class="number-cell">${totals.current.contractCustomerCount}</td>`;
    
    if (enableComparison) {
        tableHtml += `
                                    <td class="number-cell">${totals.previous.phoneCount}</td>
                                    <td class="number-cell">${totals.previous.visitCount}</td>
                                    <td class="number-cell">${totals.previous.emailCount}</td>
                                    <td class="number-cell">${totals.previous.socialMediaCount}</td>
                                    <td class="number-cell">${totals.previous.newCustomerCount}</td>
                                    <td class="number-cell">${totals.previous.contractCustomerCount}</td>`;
    }
    
    tableHtml += `
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>`;
    
    return tableHtml;
}

/**
 * 生成部门统计表格
 */
function generateDepartmentTable(enableComparison) {
    const data = currentData?.departments;
    if (!data || data.length === 0) return '<div class="no-data">暂无部门数据</div>';
    
    let tableHtml = `
        <div id="departmentTab" class="tab-content active">
            <div class="statistics-card">
                <div class="statistics-header">
                    <span>🏢 部门统计表</span>
                    <span id="departmentPeriod">${getPeriodLabel(enableComparison)}</span>
                </div>
                <div class="statistics-body">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th rowspan="2" style="width: 100px;">部门</th>
                                    <th rowspan="2" style="width: 60px;">人员数</th>
                                    <th colspan="6">当前数据</th>`;
    
    if (enableComparison) {
        tableHtml += `<th colspan="6">对比数据</th>`;
    }
    
    tableHtml += `
                                </tr>
                                <tr>
                                    <th style="width: 60px;">电话量</th>
                                    <th style="width: 60px;">拜访量</th>
                                    <th style="width: 60px;">邮件量</th>
                                    <th style="width: 60px;">社媒量</th>
                                    <th style="width: 60px;">新增客户</th>
                                    <th style="width: 60px;">签约客户</th>`;
    
    if (enableComparison) {
        tableHtml += `
                                    <th style="width: 60px;">电话量</th>
                                    <th style="width: 60px;">拜访量</th>
                                    <th style="width: 60px;">邮件量</th>
                                    <th style="width: 60px;">社媒量</th>
                                    <th style="width: 60px;">新增客户</th>
                                    <th style="width: 60px;">签约客户</th>`;
    }
    
    tableHtml += `
                                </tr>
                            </thead>
                            <tbody>`;
    
    // 计算合计
    let totals = {
        userCount: 0,
        current: { phoneCount: 0, visitCount: 0, emailCount: 0, socialMediaCount: 0, newCustomerCount: 0, contractCustomerCount: 0 },
        previous: { phoneCount: 0, visitCount: 0, emailCount: 0, socialMediaCount: 0, newCustomerCount: 0, contractCustomerCount: 0 }
    };
    
    data.forEach(dept => {
        totals.userCount += dept.userCount;
        Object.keys(totals.current).forEach(key => {
            totals.current[key] += dept.current[key] || 0;
            totals.previous[key] += dept.previous[key] || 0;
        });
        
        tableHtml += `
                                <tr>
                                    <td>${dept.name}</td>
                                    <td class="number-cell">${dept.userCount}</td>
                                    <td class="number-cell">${dept.current.phoneCount}</td>
                                    <td class="number-cell">${dept.current.visitCount}</td>
                                    <td class="number-cell">${dept.current.emailCount}</td>
                                    <td class="number-cell">${dept.current.socialMediaCount}</td>
                                    <td class="number-cell">${dept.current.newCustomerCount}</td>
                                    <td class="number-cell">${dept.current.contractCustomerCount}</td>`;
        
        if (enableComparison) {
            tableHtml += `
                                    <td class="number-cell">${dept.previous.phoneCount}</td>
                                    <td class="number-cell">${dept.previous.visitCount}</td>
                                    <td class="number-cell">${dept.previous.emailCount}</td>
                                    <td class="number-cell">${dept.previous.socialMediaCount}</td>
                                    <td class="number-cell">${dept.previous.newCustomerCount}</td>
                                    <td class="number-cell">${dept.previous.contractCustomerCount}</td>`;
        }
        
        tableHtml += `
                                </tr>`;
    });
    
    // 添加合计行
    tableHtml += `
                                <tr style="background-color: #f0f9ff; font-weight: bold;">
                                    <td>合计</td>
                                    <td class="number-cell">${totals.userCount}</td>
                                    <td class="number-cell">${totals.current.phoneCount}</td>
                                    <td class="number-cell">${totals.current.visitCount}</td>
                                    <td class="number-cell">${totals.current.emailCount}</td>
                                    <td class="number-cell">${totals.current.socialMediaCount}</td>
                                    <td class="number-cell">${totals.current.newCustomerCount}</td>
                                    <td class="number-cell">${totals.current.contractCustomerCount}</td>`;
    
    if (enableComparison) {
        tableHtml += `
                                    <td class="number-cell">${totals.previous.phoneCount}</td>
                                    <td class="number-cell">${totals.previous.visitCount}</td>
                                    <td class="number-cell">${totals.previous.emailCount}</td>
                                    <td class="number-cell">${totals.previous.socialMediaCount}</td>
                                    <td class="number-cell">${totals.previous.newCustomerCount}</td>
                                    <td class="number-cell">${totals.previous.contractCustomerCount}</td>`;
    }
    
    tableHtml += `
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>`;
    
    return tableHtml;
}

/**
 * 获取时间范围标签
 */
function getPeriodLabel(enableComparison) {
    const timeRange = document.getElementById('timeRange').value;
    let currentPeriod, previousPeriod;
    
    switch(timeRange) {
        case 'thisWeek':
            currentPeriod = '本周';
            previousPeriod = '上周';
            break;
        case 'thisMonth':
            currentPeriod = '本月';
            previousPeriod = '上月';
            break;
        case 'thisQuarter':
            currentPeriod = '本季度';
            previousPeriod = '上季度';
            break;
        case 'thisYear':
            currentPeriod = '本年';
            previousPeriod = '去年';
            break;
    }
    
    return enableComparison ? `${currentPeriod} vs ${previousPeriod}` : currentPeriod;
}

/**
 * 格式化指标值
 */
function formatIndicatorValue(value, isPercentage) {
    if (isPercentage) {
        return value + '%';
    }
    return formatNumber(value);
}

/**
 * 格式化变化值
 */
function formatChangeValue(change, isPercentage) {
    const prefix = change >= 0 ? '+' : '';
    if (isPercentage) {
        return prefix + change.toFixed(1) + '%';
    }
    return prefix + change;
}

/**
 * 获取趋势图标
 */
function getTrendIcon(trend) {
    switch(trend) {
        case 'up':
            return '↗';
        case 'down':
            return '↘';
        case 'equal':
            return '→';
        default:
            return '→';
    }
}
