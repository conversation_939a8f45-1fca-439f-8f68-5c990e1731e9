using CRM2_API.DAL.DbCommon;
using CRM2_API.DAL.DbModel.Crm2;
using CRM2_API.Model.BLLModel.Enum;
using CRM2_API.Model.ControllersViewModel;

namespace CRM2_API.DAL.DbModelOpe.Crm2
{
    /// <summary>
    /// crm_contract_productserviceinfo_saleswits_appl表操作
    /// </summary>
    public class DbOpe_crm_contract_productserviceinfo_saleswits_appl:DbOperateCrm2<Db_crm_contract_productserviceinfo_saleswits_appl,DbOpe_crm_contract_productserviceinfo_saleswits_appl>
    {
        /// <summary>
        /// 将相同contractProductInfoGropuId的数据置位失效
        /// </summary>
        /// <param name="contractProductInfoId"></param>
        public void InvalidOldApply(string contractProductInfoId)
        {
            Updateable
                .SetColumns(e => e.IsInvalid == (int)EnumIsInvalid.Invalid)
                .SetColumns(e => e.UpdateDate == DateTime.Now)
                .SetColumns(e => e.UpdateUser == UserId)
                .Where(e => e.ContractProductInfoId == contractProductInfoId)
                .Where(e => e.Deleted == false)
                .Where(e => e.IsInvalid == (int)EnumIsInvalid.Effective)
                .ExecuteCommand();
        }

        /// <summary>
        /// 把现在生效产品对应的申请,状态重新置为有效  (如果剩下也全是拒绝的那就全是无效也无所谓了)
        /// </summary>
        /// <param name="contractProductInfoId"></param>
        public void ReActiveCurrentAppl(string contractProductInfoId)
        {
            var curServe = DbOpe_crm_contract_serviceinfo_saleswits.Instance.GetData(g =>
                (g.State == EnumContractServiceState.VALID || g.State == EnumContractServiceState.TO_BE_EFFECTIVE || g.State == EnumContractServiceState.OUT)
                && g.ContractProductInfoId == contractProductInfoId
                && g.IsHistory == false
                && g.IsChanged == false
            );
            if (curServe != null)
            {
                var curAppl = GetDataById(curServe.ProductServiceInfoSalesWitsApplId);
                curAppl.IsInvalid = EnumIsInvalid.Effective.ToInt();
                Update(curAppl);
            }
        }

        /// <summary>
        /// 获取慧思产品中SalesWits的申请信息
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <returns></returns>
        public GetWitsApplyInfo4Audit_Out_ApplyInfo_SalesWits GetApplyInfoByWitsApplyId(string witsApplId)
        {
            return Queryable
                .LeftJoin<Db_crm_product>((e, f) => e.ProductId == f.Id && f.Deleted == false)
                .Where(e => e.WitsApplId == witsApplId)
                .Where(e => e.Deleted == false)
                .Select((e,f) => new GetWitsApplyInfo4Audit_Out_ApplyInfo_SalesWits
                {
                    Id = e.Id,
                    AccountNum = e.AccountsNum.Value,
                    ServiceCycleStart = e.ServiceCycleStartAfterDiscount.Value,
                    ServiceCycleEnd = e.ServiceCycleEndAfterDiscount.Value,
                    ServiceMonth = e.ServiceMonthAfterDiscount.Value,
                    ProducttId = e.ProductId,
                    ProductName = f.ProductName,
                    GiftResourceMonths = e.GiftResourceMonths.Value,
                    AddCredit = e.AddCredit.Value,
                    EmailCount = e.EmailCount.Value,
                    TokenCount = e.TokenCount.Value,
                })
                .First();
        }

        /// <summary>
        /// 根据witsId获取环球搜申请信息
        /// </summary>
        /// <param name="witsApplId"></param>
        /// <returns></returns>
        public Db_crm_contract_productserviceinfo_saleswits_appl GetSalesWitsApplByWitsId(string witsApplId)
        {
            return Queryable
                .Where(e => e.WitsApplId == witsApplId)
                .Where(e => e.Deleted == false)
                .Where(e => e.IsInvalid == (int)EnumIsInvalid.Effective)
                .First();
        }
    }
}
